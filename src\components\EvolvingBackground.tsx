'use client';

import React, { useState, useEffect, useMemo, useRef } from 'react';
import { motion, useAnimation } from 'framer-motion';
import eraVisuals from '@/data/eraVisuals.json';
import AnimatedShapes from './AnimatedShapes';
import { Era, Section } from '@/types';

interface EvolvingBackgroundProps {
  currentEra: Era | null;
  isPlaying: boolean;
  currentSection: Section | null;
  currentTime: number;
}

// Type guards remain the same
const isEraVisualData = (data: any): data is { colors: string[] } => {
  return data && typeof data === 'object' && !Array.isArray(data) && Array.isArray(data.colors);
};

const isMultiStageEra = (
  data: any
): data is { colors: string[]; fadeInDuration?: number; fadeOutDuration?: number; transitionDuration?: number; stageDuration?: number }[] => {
  return Array.isArray(data) && data.every(stage => stage && Array.isArray(stage.colors));
};

const isBranchingEra = (data: any): data is { [key: string]: { colors: string[] } } => {
  return data && typeof data === 'object' && !Array.isArray(data) && Object.values(data).every(isEraVisualData);
};

const EvolvingBackground: React.FC<EvolvingBackgroundProps> = ({ currentEra, isPlaying, currentSection, currentTime }) => {
  const backgroundControls = useAnimation();
  const sceneControls = useAnimation();
  const [stageIndex, setStageIndex] = useState(0);
  const [displayedEra, setDisplayedEra] = useState(currentEra);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const prevEraRef = useRef<Era | null>(null);


  // Helper function to get fade-out duration for an era
  const getFadeOutDuration = (eraId: string | undefined): number => {
    if (!eraId) return 2.5;

    const visualData = eraVisuals[eraId as keyof typeof eraVisuals];
    if (!visualData) return 2.5;

    let stageData: any;
    if (isEraVisualData(visualData)) {
      stageData = visualData;
    } else if (isMultiStageEra(visualData)) {
      stageData = visualData[visualData.length - 1]; // Use last stage for fade-out
    } else {
      return 2.5;
    }

    return stageData.fadeOutDuration || stageData.transitionDuration || stageData.duration || 2.5;
  };

  // This effect handles the transition logic between eras
  useEffect(() => {
    const prevEra = prevEraRef.current;
    if (currentEra?.id === displayedEra?.id) {
      prevEraRef.current = currentEra; // Keep prevEra updated even if no visual change
      return;
    }

    const shouldFadeToBlackLong = isPlaying && prevEra?.id === 'first-steps-on-land' && currentEra?.id === 'age-of-reptiles';
    const shouldUseCustomTransition = isPlaying && prevEra?.id === 'age-of-reptiles' && currentEra?.id === 'impact-and-aftermath';

    if (shouldFadeToBlackLong) {
      // 1. Fade out the entire scene slowly
      sceneControls.start({ opacity: 0, transition: { duration: 1.5 } }).then(() => {
        // 2. Once black, update the displayed era. This swaps the components.
        setDisplayedEra(currentEra);
        // 3. Fade the scene back in, revealing the new era's components.
        sceneControls.start({ opacity: 1, transition: { duration: 0.1 } });
      });
    } else if (shouldUseCustomTransition) {
      // Get the fade-out duration from the previous era (Age of Reptiles)
      const fadeOutDuration = getFadeOutDuration(prevEra?.id);

      setIsTransitioning(true);
      // Force-stop any long-running gradient animations
      backgroundControls.stop();

      // Use the fade-out duration from Age of Reptiles (which is 0 = instant)
      backgroundControls
        .start({
          '--gradient-color-1': '#000000',
          '--gradient-color-2': '#000000',
          '--gradient-color-3': '#000000',
          transition: { duration: fadeOutDuration }, // Use Age of Reptiles fade-out duration (0)
        })
        .then(() => {
          // Change era immediately after fade-out completes
          setDisplayedEra(currentEra);
          setIsTransitioning(false);
        });
    } else {
      // For all other transitions, just swap the content directly.
      setDisplayedEra(currentEra);
    }

    // Keep track of the previous era for the next transition.
    prevEraRef.current = currentEra;
  }, [currentEra, isPlaying, sceneControls, displayedEra, backgroundControls]);

  const { colors, fadeInDuration, fadeOutDuration } = useMemo(() => {
    const defaultVisuals = { colors: ['#000000', '#000000', '#000000'], fadeInDuration: 2.5, fadeOutDuration: 2.5 };
    if (!displayedEra) return defaultVisuals;

    const visualData = eraVisuals[displayedEra.id as keyof typeof eraVisuals];
    if (!visualData) return defaultVisuals;

    let currentStageData: any;
    if (displayedEra.subEra && isBranchingEra(visualData)) {
      currentStageData = visualData[displayedEra.subEra];
    } else if (isMultiStageEra(visualData)) {
      currentStageData = visualData[stageIndex] || visualData[visualData.length - 1];
    } else if (isEraVisualData(visualData)) {
      currentStageData = visualData;
    } else {
      return defaultVisuals;
    }

    // Support both new fade-in/fade-out structure and legacy transitionDuration/duration
    const fadeIn = currentStageData.fadeInDuration || currentStageData.transitionDuration || currentStageData.duration || 2.5;
    const fadeOut = currentStageData.fadeOutDuration || currentStageData.transitionDuration || currentStageData.duration || 2.5;

    return {
      colors: currentStageData.colors,
      fadeInDuration: fadeIn,
      fadeOutDuration: fadeOut,
    };
  }, [displayedEra, stageIndex]);

  // This effect controls the background gradient animation for the *displayed* era
  useEffect(() => {
    if (isTransitioning) return; // Do not run this effect during a special transition

    if (isPlaying && displayedEra) {
      const isReptiles = displayedEra.id === 'age-of-reptiles';
      const isImpact = displayedEra.id === 'impact-and-aftermath';

      let effectiveDuration = fadeInDuration;
      let effectiveDelay = 0;

      if (isReptiles) {
        effectiveDuration = 10;
        effectiveDelay = 1;
      } else if (isImpact) {
        // When the impact era is displayed, its background transition should be very fast.
        effectiveDuration = fadeInDuration; // Use the configured fade-in duration (0.1)
      }

      backgroundControls.start({
        opacity: 1,
        '--gradient-color-1': colors[0],
        '--gradient-color-2': colors[1],
        '--gradient-color-3': colors[2],
        transition: { duration: effectiveDuration, ease: 'easeInOut', delay: effectiveDelay },
      });
    } else {
      backgroundControls.start({ opacity: 0, transition: { duration: 1 } });
    }
  }, [isPlaying, displayedEra, colors, fadeInDuration, backgroundControls, isTransitioning]);

  // This effect handles multi-stage eras like Primordial Soup
  useEffect(() => {
    if (!displayedEra || !isPlaying) {
      setStageIndex(0);
      return;
    }

    const visualData = eraVisuals[displayedEra.id as keyof typeof eraVisuals];
    if (!isMultiStageEra(visualData)) {
      setStageIndex(0);
      return;
    }

    let accumulatedDelay = 0;
    const timeouts = visualData.slice(0, -1).map((stage, index) => {
      if (typeof stage.stageDuration !== 'number') return null;
      accumulatedDelay += stage.stageDuration;
      return setTimeout(() => {
        setStageIndex(index + 1);
      }, accumulatedDelay);
    });

    return () => {
      timeouts.forEach(timeoutId => {
        if (timeoutId) clearTimeout(timeoutId);
      });
      setStageIndex(0);
    };
  }, [displayedEra, isPlaying]);

  return (
    <motion.div className="absolute inset-0" animate={sceneControls} initial={{ opacity: 1 }}>
      <motion.div
        data-testid="evolving-background"
        className="absolute inset-0 -z-20 evolving-gradient-bg"
        initial={{
          opacity: 0,
          '--gradient-color-1': '#000000',
          '--gradient-color-2': '#000000',
          '--gradient-color-3': '#000000',
        }}
        animate={backgroundControls}
        style={{ backgroundColor: '#000000' }}
      />
      <div className="absolute inset-0">
        <AnimatedShapes
          currentEra={displayedEra}
          isPlaying={isPlaying}
          currentSection={currentSection}
          currentTime={currentTime}
        />
      </div>
    </motion.div>
  );
};

export default EvolvingBackground;

