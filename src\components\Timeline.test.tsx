import { render, screen } from '@testing-library/react';
import Timeline from './Timeline';
import timelineData from '@/data/timeline.json';
import React from 'react';

// Mock the cn utility
jest.mock('@/lib/utils', () => ({
  cn: (...args: (string | undefined | null | boolean)[]) =>
    args.filter(Boolean).join(' '),
}));

describe('Timeline Component', () => {
  const mockProps = {
    sections: timelineData,
    duration: 600, // Mock total duration
    onSeek: jest.fn(),
  };

  // By spying on useMemo and calling the original function, we ensure the component's
  // complex calculation logic runs during the test.
  beforeEach(() => {
    jest.spyOn(React, 'useMemo').mockImplementation(fn => fn());
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('renders all timeline segments with correct relative widths', () => {
    const { container } = render(<Timeline {...mockProps} currentTime={0} />);
    const segmentContainers = container.querySelectorAll('.relative.group');
    expect(segmentContainers.length).toBe(timelineData.length);

    segmentContainers.forEach((segmentContainer, index) => {
      const section = timelineData[index];
      let expectedWidth = 0;
      if (section.barWidthPercent) {
        expectedWidth = section.barWidthPercent * 0.8;
      } else if (section.id === 12) {
        // Choice Corridor
        expectedWidth = 10;
      } else if (section.id === 13) {
        // Future
        expectedWidth = 10;
      }

      const receivedWidth = parseFloat(
        (segmentContainer as HTMLElement).style.width
      );
      expect(receivedWidth).toBeCloseTo(expectedWidth);
    });
  });

  it('positions the "NOW" marker correctly at key time points', () => {
    const { rerender } = render(<Timeline {...mockProps} currentTime={0} />);
    const nowMarker = screen.getByTestId('now-marker');

    const testCases = [
      { time: 0, percent: 0 }, // Start
      { time: 480, percent: 80 }, // End of geological eras
      { time: 540, percent: 90 }, // End of choice corridor
      { time: 600, percent: 100 }, // End of timeline
      { time: 700, percent: 100 }, // Beyond the end (capped)
    ];

    for (const { time, percent } of testCases) {
      rerender(<Timeline {...mockProps} currentTime={time} />);
      const style = nowMarker.getAttribute('style');
      expect(style).not.toBeNull();

      // Use regex to extract the percentage from a style like "left: calc(79.992% - 16px);"
      const match = style!.match(/left: calc\(([-.0-9]+)%/);
      const receivedPercent = match ? parseFloat(match[1]) : null;

      expect(receivedPercent).not.toBeNull();
      expect(receivedPercent).toBeCloseTo(percent, 1); // Allow for small floating point inaccuracies
    }
  });

  it('renders tooltip information for each segment', () => {
    render(<Timeline {...mockProps} currentTime={0} />);

    for (const section of timelineData) {
      // The tooltip content is built from these properties.
      // Note: This checks for presence in the document, assuming tooltips are rendered but hidden.
      if (section.realWorldSpan) {
        expect(
          screen.getByText(`Span: ${section.realWorldSpan}`)
        ).toBeInTheDocument();
      }
      if (section.durationMa) {
        expect(
          screen.getByText(`Duration: ${section.durationMa} Million Years`)
        ).toBeInTheDocument();
      }
    }
  });
});
