import { render, screen } from '@testing-library/react';
import Home from './page';

// Mock the useRouter hook
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    refresh: jest.fn(),
  }),
}));

describe('Home Page', () => {
  it('renders the main heading and the start button', () => {
    render(<Home />);

    const heading = screen.getByRole('heading', {
      name: /A Journey Through 4 Billion Years/i,
    });

    const startButton = screen.getByRole('button', {
      name: /begin the journey/i,
    });

    expect(heading).toBeInTheDocument();
    expect(startButton).toBeInTheDocument();
  });
});
