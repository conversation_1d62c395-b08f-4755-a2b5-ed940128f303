'use client';

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

interface ImpactAndAftermathShapesProps {
  progress: number;
}

const ImpactAndAftermathShapes: React.FC<ImpactAndAftermathShapesProps> = ({ progress }) => {
  const ashCount = 300;
  const windStreakCount = 25; // Reduced count for performance, increased size for coverage
  const rockCount = 40; // Reduced count for performance

  // --- Memoized Animation Data --- //

  const windStreaks = useMemo(() => {
    return Array.from({ length: windStreakCount }).map((_, i) => {
      const direction = Math.random() > 0.5 ? 1 : -1; // 1 for LTR, -1 for RTL
      return {
        id: `wind-${i}`,
        top: `${Math.random() * 100}%`,
        // Start off-screen on the correct side
        left: direction === 1 ? `${Math.random() * -20 - 5}%` : `${Math.random() * 20 + 85}%`,
        width: `${Math.random() * 60 + 40}vw`,
        height: `${Math.random() * 2 + 1}px`,
        duration: Math.random() * 2 + 1.5,
        delay: Math.random() * 10, // <PERSON><PERSON> starts over 10 seconds
        // Animate across the screen in the chosen direction
        x: `${direction * 120}vw`,
      };
    });
  }, []);

  const rocks = useMemo(() => {
    return Array.from({ length: rockCount }).map((_, i) => ({
      id: `rock-${i}`,
      // Start anywhere on screen
      top: `${Math.random() * 100}%`,
      left: `${Math.random() * 100}%`,
      size: Math.random() * 40 + 10, // Wider size range
      borderRadius: `${Math.random() * 40 + 20}% ${Math.random() * 40 + 20}%`,
      color: `hsl(0, 0%, ${Math.random() * 20 + 10}%)`,
      duration: Math.random() * 2.5 + 1.5, // Longer flight time
      delay: Math.random() * 8, // Stagger explosions over 8 seconds
      // Explode outwards from the start point to a random destination
      x: `${(Math.random() - 0.5) * 70}vw`,
      y: `${(Math.random() - 0.5) * 70}vh`,
    }));
  }, []);

  const ash = useMemo(() => {
    return Array.from({ length: ashCount }).map((_, i) => ({
      id: `ash-${i}`,
      x: Math.random() * 100,
      y: -Math.random() * 100 - 10,
      size: Math.random() * 4 + 1,
      duration: Math.random() * 8 + 8,
      delay: Math.random() * 16,
      baseOpacity: Math.random() * 0.5 + 0.2,
    }));
  }, []);

  // --- Visibility and Opacity Control --- //

  // Wind/rocks should end well before the era fade-out begins to avoid visual artifacts
  // Era ends at 260s with 2.5s fade-out starting ~257.5s, so we need effects to end by ~60% progress
  const impactOpacity = Math.max(0, 1 - (progress - 0.3) / 0.3); // Fades out between 30% and 60%

  // Ash rain fades in more slowly and continues through the era transition
  // It should persist through the end of the era to provide atmospheric continuity
  const ashOpacity = Math.min(1, Math.max(0, (progress - 0.2) / 0.3)); // Fades in between 20% and 50%, then stays visible

  return (
    <div className="absolute inset-0 overflow-hidden" data-testid="impact-container">
      {/* --- High-Performance Impact Animations --- */}
      <motion.div style={{ opacity: impactOpacity }}>
          {/* Wind Streaks */}
          {windStreaks.map((streak) => (
            <motion.div
              key={streak.id}
              className="absolute"
              style={{ top: streak.top, left: streak.left, width: streak.width, height: streak.height, background: 'linear-gradient(to right, rgba(200, 200, 200, 0), rgba(220, 220, 220, 0.3), rgba(200, 200, 200, 0))' }}
              initial={{ x: 0, opacity: 0 }}
              animate={{ x: streak.x, opacity: [0, 1, 1, 0] }}
              transition={{
                duration: streak.duration,
                delay: streak.delay,
                ease: 'linear',
                repeat: Infinity,
                repeatType: 'loop',
                repeatDelay: 1, // Add a small delay before repeating
              }}
            />
          ))}

          {/* Rock Explosions */}
          {rocks.map((rock) => (
            <motion.div
              key={rock.id}
              className="absolute"
              style={{ top: rock.top, left: rock.left, width: rock.size, height: rock.size, backgroundColor: rock.color, borderRadius: rock.borderRadius }}
              initial={{ x: 0, y: 0, opacity: 0, scale: 0.5, rotate: 0 }}
              animate={{ x: rock.x, y: rock.y, opacity: [0, 1, 0], scale: 1, rotate: (Math.random() - 0.5) * 720 }}
              transition={{ duration: rock.duration, delay: rock.delay, ease: 'easeOut' }}
            />
          ))}
        </motion.div>

      {/* --- High-Performance Ash Rain --- */}
      <motion.div style={{ opacity: ashOpacity }}>
        {ash.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute"
            style={{ left: `${particle.x}%`, top: particle.y, width: particle.size, height: particle.size * 2, backgroundColor: '#333', borderRadius: '50%' }}
            initial={{ y: 0, opacity: 0 }}
            animate={{ y: '110vh', opacity: [0, particle.baseOpacity, 0] }}
            transition={{ duration: particle.duration, delay: particle.delay, repeat: Infinity, repeatType: 'loop', ease: 'linear' }}
          />
        ))}
      </motion.div>
    </div>
  );
};

export default ImpactAndAftermathShapes;
