import React from 'react';
import { render, screen } from '@testing-library/react';
import AnimatedShapes from './AnimatedShapes';
import { Era, Section } from '@/types';

// Mock the child components to isolate the AnimatedShapes controller logic
jest.mock('./animations/PrimordialSoupShapes', () => () => <div data-testid="primordial-soup-shapes" />);
jest.mock('./animations/MicrobialEdenShapes', () => (props: any) => (
  <div data-testid="microbial-eden-shapes" data-props={JSON.stringify(props)} />
));
jest.mock('./animations/CambrianBurstShapes', () => () => <div data-testid="cambrian-burst-shapes" />);

describe('AnimatedShapes', () => {
  const mockPrimordialSection: Section = { id: 1, start: 0, end: 45, section: 'Primordial Soup', keySoundElements: '', visualCue: '', textCue: '' };
  const mockEdenSection: Section = { id: 2, start: 45, end: 90, section: 'Microbial Eden', keySoundElements: '', visualCue: '', textCue: '' };
  const mockCambrianSection: Section = { id: 3, start: 90, end: 140, section: 'Cambrian Burst', keySoundElements: '', visualCue: '', textCue: '' };

  const primordialEra: Era = { id: 'primordialSoup' };
  const edenEra: Era = { id: 'microbialEden' };
  const cambrianEra: Era = { id: 'cambrianBurst' };

  it('renders nothing when isPlaying is false', () => {
    render(
      <AnimatedShapes
        currentEra={primordialEra}
        currentSection={mockPrimordialSection}
        currentTime={10}
        isPlaying={false}
      />
    );
    expect(screen.queryByTestId('primordial-soup-shapes')).not.toBeInTheDocument();
  });

  it('renders PrimordialSoupShapes for the primordialSoup era', () => {
    render(
      <AnimatedShapes
        currentEra={primordialEra}
        currentSection={mockPrimordialSection}
        currentTime={10}
        isPlaying={true}
      />
    );
    expect(screen.getByTestId('primordial-soup-shapes')).toBeInTheDocument();
  });

  it('renders MicrobialEdenShapes for the microbialEden era', () => {
    render(
      <AnimatedShapes
        currentEra={edenEra}
        currentSection={mockEdenSection}
        currentTime={60}
        isPlaying={true}
      />
    );
    expect(screen.getByTestId('microbial-eden-shapes')).toBeInTheDocument();
    expect(screen.queryByTestId('cambrian-burst-shapes')).not.toBeInTheDocument();
  });

  it('renders both MicrobialEdenShapes and CambrianBurstShapes for the cambrianBurst era', () => {
    render(
      <AnimatedShapes
        currentEra={cambrianEra}
        currentSection={mockCambrianSection}
        currentTime={100}
        isPlaying={true}
      />
    );
    expect(screen.getByTestId('microbial-eden-shapes')).toBeInTheDocument();
    expect(screen.getByTestId('cambrian-burst-shapes')).toBeInTheDocument();
  });

  it('calculates targetCellCount correctly for microbialEden era', () => {
    render(
      <AnimatedShapes
        currentEra={edenEra}
        currentSection={mockEdenSection}
        currentTime={67.5} // 50% progress in Eden era
        isPlaying={true}
      />
    );
    const edenShapes = screen.getByTestId('microbial-eden-shapes');
    const props = JSON.parse(edenShapes.getAttribute('data-props') || '{}');
    // Calculation: 10 * 0.5 + 90 * 0.5^8 = 5 + 0.35 = 5.35 -> floor is 5
    expect(props.targetCellCount).toBe(5);
  });

  it('calculates targetCellCount and evolutionChance correctly for cambrianBurst era', () => {
    render(
      <AnimatedShapes
        currentEra={cambrianEra}
        currentSection={mockCambrianSection}
        currentTime={115} // 50% progress in Cambrian era (115-90)/(140-90) = 25/50 = 0.5
        isPlaying={true}
      />
    );
    const edenShapes = screen.getByTestId('microbial-eden-shapes');
    const props = JSON.parse(edenShapes.getAttribute('data-props') || '{}');
    // targetCellCount: 100 + floor(400 * 0.5^3) = 100 + floor(50) = 150
    expect(props.targetCellCount).toBe(150);
    // evolutionChance: min(0.05, 0.1 * 0.5) = 0.05
    expect(props.evolutionChance).toBe(0.05);
  });
});
