'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, motionValue, animate, MotionValue } from 'framer-motion';

// Represents the state for a single Cambrian creature
interface CreatureData {
  id: number;
  type: 'trilobite' | 'anomalocaris';
  x: MotionValue<number>;
  y: MotionValue<number>;
  size: number;
  rotation: MotionValue<number>;
  spawnedFromEvolution?: boolean;
  animationState: { isRunning: boolean };
}

interface EvolutionEvent {
  id: number;
  x: number;
  y: number;
  size: number;
}

interface CambrianBurstShapesProps {
  evolutionQueue: EvolutionEvent[];
  onEvolutionComplete: () => void;
}

// --- Individual Creature Components ---

const Trilobite: React.FC<{ creature: CreatureData }> = ({ creature }) => (
  <motion.div
    data-testid={`creature-${creature.id}`}
    className="absolute"
    style={{
      x: creature.x,
      y: creature.y,
      rotate: creature.rotation,
      width: creature.size,
      height: creature.size / 2,
      backgroundColor: '#8B4513',
      borderRadius: '50% 50% 40% 40% / 60% 60% 40% 40%',
      border: '2px solid #A0522D',
    }}
    initial={creature.spawnedFromEvolution
      ? { opacity: 0, scale: 0, filter: 'brightness(10) blur(8px)' }
      : { opacity: 0, scale: 0.5 }
    }
    animate={{
      opacity: 1,
      scale: 1,
      filter: 'brightness(1) blur(0px)',
      transition: { duration: 1.5, ease: 'easeOut' },
    }}
    exit={{ opacity: 0, scale: 0, transition: { duration: 1 } }}
  />
);

const Anomalocaris: React.FC<{ creature: CreatureData }> = ({ creature }) => (
  <motion.div
    data-testid={`creature-${creature.id}`}
    className="absolute"
    style={{
      x: creature.x,
      y: creature.y,
      rotate: creature.rotation,
      width: creature.size * 1.5,
      height: creature.size / 3,
      backgroundColor: '#4682B4',
      borderRadius: '10px',
      border: '2px solid #5F9EA0',
    }}
    initial={creature.spawnedFromEvolution
      ? { opacity: 0, scale: 0, filter: 'brightness(10) blur(8px)' }
      : { opacity: 0, scale: 0.5 }
    }
    animate={{
      opacity: 1,
      scale: 1,
      filter: 'brightness(1) blur(0px)',
      transition: { duration: 1.5, ease: 'easeOut' },
    }}
    exit={{ opacity: 0, scale: 0, transition: { duration: 1 } }}
  />
);

const creatureComponents = {
  trilobite: Trilobite,
  anomalocaris: Anomalocaris,
};

// --- Main Component ---

const CambrianBurstShapes: React.FC<CambrianBurstShapesProps> = ({ evolutionQueue, onEvolutionComplete }) => {
  const [creatures, setCreatures] = useState<CreatureData[]>([]);
  const creaturesRef = useRef(creatures);
  creaturesRef.current = creatures;
  const windowSize = useRef({ width: 0, height: 0 });

  useEffect(() => {
    windowSize.current = { width: window.innerWidth, height: window.innerHeight };
  }, []);

  // Effect to handle spawning from evolution events
  useEffect(() => {
    if (evolutionQueue.length > 0) {
      setCreatures(currentCreatures => {
        const newCreaturesFromQueue = evolutionQueue.map(event => {
          const type = Math.random() > 0.5 ? 'trilobite' : 'anomalocaris';
          const newCreature: CreatureData = {
            id: event.id,
            type,
            x: motionValue(event.x),
            y: motionValue(event.y),
            size: type === 'trilobite' ? 30 : 40,
            rotation: motionValue(0),
            spawnedFromEvolution: true,
            animationState: { isRunning: true },
          };
          startMovement(newCreature);
          return newCreature;
        });
        return [...currentCreatures, ...newCreaturesFromQueue];
      });
      onEvolutionComplete();
    }
  }, [evolutionQueue, onEvolutionComplete]);

  // Effect to stop animations on unmount
  useEffect(() => {
    return () => {
      // When the component unmounts, stop all creature animations
      creaturesRef.current.forEach(c => (c.animationState.isRunning = false));
    };
  }, []); // Empty dependency array means this runs only on mount and unmount

  const startMovement = (creature: CreatureData) => {
    let currentAngle = Math.random() * 2 * Math.PI;

    const animateWander = async () => {
      // 1. Calculate next position with a slight random deviation
      currentAngle += (Math.random() - 0.5) * 0.9; // Adjust angle up to ~25 degrees

      const stepDistance = creature.type === 'trilobite' 
        ? Math.random() * 40 + 20   // Trilobites: shorter, jerkier steps
        : Math.random() * 80 + 40;  // Anomalocaris: longer, smoother glides

      const duration = creature.type === 'trilobite' 
        ? Math.random() * 1 + 1.0   // 1.0s - 2.0s
        : Math.random() * 2 + 2.5;  // 2.5s - 4.5s

      let nextX = creature.x.get() + Math.cos(currentAngle) * stepDistance;
      let nextY = creature.y.get() + Math.sin(currentAngle) * stepDistance;

      // 2. Wall collision and bounce logic
      const margin = creature.size * 2;
      if (nextX < margin || nextX > windowSize.current.width - margin) {
        nextX = Math.max(margin, Math.min(windowSize.current.width - margin, nextX));
        currentAngle = Math.PI - currentAngle;
      }
      if (nextY < margin || nextY > windowSize.current.height - margin) {
        nextY = Math.max(margin, Math.min(windowSize.current.height - margin, nextY));
        currentAngle = -currentAngle;
      }

      // 3. Animate to the next position
      try {
        await Promise.all([
          animate(creature.x, nextX, { duration, ease: 'easeOut' }),
          animate(creature.y, nextY, { duration, ease: 'easeOut' }),
          animate(creature.rotation, (currentAngle * 180) / Math.PI, { duration: 0.5 }),
        ]);
      } catch (e) {
        // Animation was interrupted (e.g., component unmount), so we stop.
        return;
      }
      
      // 4. Loop the animation
      if (creature.animationState.isRunning) {
        animateWander();
      }
    };

    animateWander();
  };

  return (
    <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
      <AnimatePresence>
        {creatures.map((creature) => {
          const CreatureComponent = creatureComponents[creature.type];
          return <CreatureComponent key={creature.id} creature={creature} />;
        })}
      </AnimatePresence>
    </div>
  );
};

export default CambrianBurstShapes;
