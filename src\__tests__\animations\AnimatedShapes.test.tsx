import React from 'react';
import { render, screen, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import AnimatedShapes from '@/components/AnimatedShapes';
import { Era, Section } from '@/types';

// Define test data locally instead of importing from a non-existent file
const ERAS: { [key: string]: Era } = {
  primordialSoup: { id: 'primordialSoup', subEra: null },
  microbialEden: { id: 'microbialEden', subEra: null },
  cambrianBurst: { id: 'cambrianBurst', subEra: null },
};

const SECTIONS: Section[] = [
  { id: 1, section: 'Primordial Soup', start: 0, end: 45, keySoundElements: '', visualCue: '', textCue: '' },
  { id: 2, section: 'Microbial Eden', start: 45, end: 90, keySoundElements: '', visualCue: '', textCue: '' },
  { id: 3, section: 'Cambrian Burst', start: 90, end: 140, keySoundElements: '', visualCue: '', textCue: '' },
];

// Mock child components to isolate the AnimatedShapes logic
jest.mock('@/components/animations/PrimordialSoupShapes', () => () => <div data-testid="primordial-soup"></div>);
jest.mock('@/components/animations/MicrobialEdenShapes', () => (props: any) => (
  <div data-testid="microbial-eden" onClick={() => props.onEvolve(100, 150, 20)}></div>
));
jest.mock('@/components/animations/CambrianBurstShapes', () => (props: any) => (
  <div data-testid="cambrian-burst">{JSON.stringify(props.evolutionQueue)}</div>
));

describe('AnimatedShapes', () => {
  it('should not render any shapes when era is null', () => {
    render(<AnimatedShapes currentEra={null} currentSection={null} currentTime={0} isPlaying={true} />);
    expect(screen.queryByTestId('primordial-soup')).not.toBeInTheDocument();
  });

  it('should render PrimordialSoupShapes for the primordialSoup era', () => {
    render(<AnimatedShapes currentEra={ERAS.primordialSoup} currentSection={SECTIONS[0]} currentTime={10} isPlaying={true} />);
    expect(screen.getByTestId('primordial-soup')).toBeInTheDocument();
  });

  it('should handle the evolution flow between MicrobialEden and CambrianBurst', () => {
    const { rerender } = render(
      <AnimatedShapes currentEra={ERAS.cambrianBurst} currentSection={SECTIONS[2]} currentTime={100} isPlaying={true} />
    );

    // 1. Initially, the evolution queue is empty
    const cambrianBurstComponent = screen.getByTestId('cambrian-burst');
    expect(cambrianBurstComponent.textContent).toBe('[]');

    // 2. Simulate a cell evolving by clicking the mock MicrobialEden component
    const microbialEdenComponent = screen.getByTestId('microbial-eden');
    act(() => {
      microbialEdenComponent.click();
    });

    // 3. Re-render to see the effect of the state update
    rerender(<AnimatedShapes currentEra={ERAS.cambrianBurst} currentSection={SECTIONS[2]} currentTime={100} isPlaying={true} />);

    // 4. Check if the evolution event was added to the queue and passed to CambrianBurstShapes
    const cambrianBurstComponentAfterEvolve = screen.getByTestId('cambrian-burst');
    const queueData = JSON.parse(cambrianBurstComponentAfterEvolve.textContent || '[]');
    expect(queueData).toHaveLength(1);
    expect(queueData[0]).toMatchObject({ x: 100, y: 150, size: 20 });
  });
});
