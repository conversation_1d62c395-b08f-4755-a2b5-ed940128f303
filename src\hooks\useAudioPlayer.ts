'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

interface UseAudioPlayerProps {
  initialSrc: string;
}

interface AudioPlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  play: () => void;
  pause: () => void;
  changeSource: (newSrc: string, playWhenReady?: boolean) => void;
  seek: (time: number, duration?: number) => void;
  setVolume: (volume: number) => void;
}

export const useAudioPlayer = ({ initialSrc }: UseAudioPlayerProps): AudioPlayerState => {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolumeState] = useState(1);

  useEffect(() => {
    if (typeof window !== 'undefined' && !audioRef.current) {
      audioRef.current = new Audio(initialSrc);

      const setAudioData = () => {
        setDuration(audioRef.current?.duration || 0);
        setCurrentTime(audioRef.current?.currentTime || 0);
      };

      const setAudioTime = () => setCurrentTime(audioRef.current?.currentTime || 0);
      const handlePlay = () => setIsPlaying(true);
      const handlePause = () => setIsPlaying(false);

      audioRef.current.addEventListener('loadeddata', setAudioData);
      audioRef.current.addEventListener('timeupdate', setAudioTime);
      audioRef.current.addEventListener('play', handlePlay);
      audioRef.current.addEventListener('pause', handlePause);

      // cleanup
      return () => {
        audioRef.current?.removeEventListener('loadeddata', setAudioData);
        audioRef.current?.removeEventListener('timeupdate', setAudioTime);
        audioRef.current?.removeEventListener('play', handlePlay);
        audioRef.current?.removeEventListener('pause', handlePause);
        audioRef.current?.pause();
        audioRef.current = null;
      };
    }
  }, [initialSrc]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
    }
  }, [volume]);

  const play = useCallback(() => {
    audioRef.current?.play();
  }, []);

  const pause = useCallback(() => {
    audioRef.current?.pause();
  }, []);

  const changeSource = useCallback((newSrc: string, playWhenReady = true) => {
    if (audioRef.current) {
      const time = audioRef.current.currentTime;
      
      audioRef.current.src = newSrc;
      audioRef.current.load();
      
      const onCanPlay = () => {
        if (audioRef.current) {
          audioRef.current.currentTime = time;
          if (playWhenReady) {
            audioRef.current.play();
          }
        }
        audioRef.current?.removeEventListener('canplay', onCanPlay);
      };
      audioRef.current.addEventListener('canplay', onCanPlay);
    }
  }, []);

  const seek = useCallback((time: number, newDuration?: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time;
      setCurrentTime(time);
      if (newDuration !== undefined) {
        setDuration(newDuration);
      }
    }
  }, []);

  const setVolume = useCallback((newVolume: number) => {
    if (audioRef.current) {
      const clampedVolume = Math.max(0, Math.min(1, newVolume));
      audioRef.current.volume = clampedVolume;
      setVolumeState(clampedVolume);
    }
  }, []);

  return { isPlaying, currentTime, duration, volume, play, pause, changeSource, seek, setVolume };
};

