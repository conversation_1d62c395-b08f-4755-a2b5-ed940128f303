'use client';

import React from 'react';
import { motion } from 'framer-motion';

// More organic wing paths
const topWingUp = 'M5,10 C2.5,5 0,2 0,0';
const topWingDown = 'M5,10 C2.5,15 0,18 0,20';
const bottomWingUp = 'M5,10 C7.5,5 10,2 10,0';
const bottomWingDown = 'M5,10 C7.5,15 10,18 10,20';

interface Bird {
  id: number;
  initialX: string;
  initialY: string;
  scale: number;
  duration: number;
  delay: number;
  flapDuration: number;
}

// Reduce bird count, increase duration range, and stagger delay
const birds: Bird[] = Array.from({ length: 8 }).map((_, i) => {
  const minScale = 0.3;
  const maxScale = 1.5;
  const scale = minScale + Math.random() * (maxScale - minScale);

  const minDuration = 30; // Slower
  const maxDuration = 65; // Slower

  const duration = maxDuration - ((scale - minScale) / (maxScale - minScale)) * (maxDuration - minDuration);

  return {
    id: i,
    initialX: '110vw',
    initialY: `${50 + Math.random() * 40}vh`,
    scale,
    duration,
    delay: Math.random() * 25, // More spread out
    flapDuration: 0.4 + Math.random() * 0.4, // Random duration between 0.4s and 0.8s
  };
});

const DistantBirds: React.FC = () => {
  return (
    <div className="absolute inset-0 w-full h-full overflow-hidden" style={{ zIndex: 1 }}>
      {birds.map((bird) => (
        <motion.div
          key={bird.id}
          className="absolute"
          style={{ x: bird.initialX, y: bird.initialY, scale: bird.scale }}
          animate={{
            x: '-10vw',
          }}
          transition={{
            duration: bird.duration,
            delay: bird.delay,
            repeat: Infinity,
            repeatType: 'loop',
            ease: 'linear',
          }}
        >
          {/* Inner div for vertical bobbing motion */}
          <motion.div
            animate={{ y: ['0%', '-10%', '0%'] }}
            transition={{
              duration: bird.flapDuration,
              repeat: Infinity,
              repeatType: 'loop',
              ease: 'easeInOut',
            }}
          >
            <svg width="10" height="20" viewBox="0 0 10 20" fill="none" stroke="#211F1D" strokeWidth="1.5">
              <motion.path
                d={topWingUp}
                animate={{ d: [topWingUp, topWingDown, topWingUp] }}
                transition={{
                  duration: bird.flapDuration,
                  repeat: Infinity,
                  repeatType: 'loop',
                  ease: 'easeInOut',
                }}
              />
              <motion.path
                d={bottomWingUp}
                animate={{ d: [bottomWingUp, bottomWingDown, bottomWingUp] }}
                transition={{
                  duration: bird.flapDuration,
                  repeat: Infinity,
                  repeatType: 'loop',
                  ease: 'easeInOut',
                }}
              />
            </svg>
          </motion.div>
        </motion.div>
      ))}
    </div>
  );
};

export default DistantBirds;

