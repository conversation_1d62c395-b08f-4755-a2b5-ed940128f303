import React from 'react';
import { render, screen } from '@testing-library/react';
import EvolvingBackground from '@/components/EvolvingBackground';
import { Era } from '@/types';

// Mock framer-motion's useAnimation hook
const mockStart = jest.fn().mockResolvedValue(undefined);
const mockStop = jest.fn();
jest.mock('framer-motion', () => ({
  ...jest.requireActual('framer-motion'),
  useAnimation: () => ({
    start: mockStart,
    stop: mockStop
  }),
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock AnimatedShapes component
jest.mock('./AnimatedShapes', () => ({ currentEra, isPlaying, currentSection, currentTime }: any) => (
  <div data-testid="animated-shapes" data-era={currentEra?.id} data-playing={isPlaying} />
));

// Mock the era visuals data
jest.mock('@/data/eraVisuals.json', () => ({
  'primordial-soup': {
    colors: ['#0a0a0a', '#1a1a1a', '#2a2a2a'],
    fadeInDuration: 2.5,
    fadeOutDuration: 2.5,
  },
  'multi-stage-era': [
    {
      colors: ['#ff0000', '#00ff00', '#0000ff'],
      stageDuration: 5000, // 5s
      fadeInDuration: 1,
      fadeOutDuration: 1,
    },
    {
      colors: ['#ffff00', '#ff00ff', '#00ffff'],
      fadeInDuration: 1.5,
      fadeOutDuration: 1.5,
    },
  ],
  'age-of-reptiles': {
    colors: ['#FAD7A0', '#FDB813', '#E67E22'],
    fadeInDuration: 12,
    fadeOutDuration: 0,
  },
  'impact-and-aftermath': {
    colors: ['#000000', '#000000', '#000000'],
    fadeInDuration: 0.1,
    fadeOutDuration: 2.5,
  },
}));

describe('EvolvingBackground', () => {
  // Era types for testing
  const primordialSoupEra: Era = { id: 'primordial-soup' };
  const ageOfReptilesEra: Era = { id: 'age-of-reptiles' };
  const impactAndAftermathEra: Era = { id: 'impact-and-aftermath' };

  // Default props for tests
  const defaultSection = null;
  const defaultCurrentTime = 0;

  beforeEach(() => {
    jest.useFakeTimers();
    mockStart.mockClear();
    mockStop.mockClear();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders without crashing when not playing', () => {
    render(<EvolvingBackground currentEra={primordialSoupEra} isPlaying={false} currentSection={defaultSection} currentTime={defaultCurrentTime} />);
    expect(screen.getByTestId('evolving-background')).toBeInTheDocument();
  });

  it('renders without crashing when playing', () => {
    render(<EvolvingBackground currentEra={primordialSoupEra} isPlaying={true} currentSection={defaultSection} currentTime={defaultCurrentTime} />);
    expect(screen.getByTestId('evolving-background')).toBeInTheDocument();
  });

  it('renders with Age of Reptiles era', () => {
    render(<EvolvingBackground currentEra={ageOfReptilesEra} isPlaying={true} currentSection={defaultSection} currentTime={defaultCurrentTime} />);
    expect(screen.getByTestId('evolving-background')).toBeInTheDocument();
  });

  it('renders with Impact and Aftermath era', () => {
    render(<EvolvingBackground currentEra={impactAndAftermathEra} isPlaying={true} currentSection={defaultSection} currentTime={defaultCurrentTime} />);
    expect(screen.getByTestId('evolving-background')).toBeInTheDocument();
  });

  it('handles era transitions without crashing', () => {
    // Test that the component can handle era transitions
    const { rerender } = render(
      <EvolvingBackground
        currentEra={ageOfReptilesEra}
        isPlaying={true}
        currentSection={defaultSection}
        currentTime={defaultCurrentTime}
      />
    );

    expect(screen.getByTestId('evolving-background')).toBeInTheDocument();

    // Transition to Impact & Aftermath era should not crash
    rerender(
      <EvolvingBackground
        currentEra={impactAndAftermathEra}
        isPlaying={true}
        currentSection={defaultSection}
        currentTime={defaultCurrentTime}
      />
    );

    expect(screen.getByTestId('evolving-background')).toBeInTheDocument();
  });
});
