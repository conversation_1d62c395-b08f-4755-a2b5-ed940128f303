import React from 'react';
import { render, screen } from '@testing-library/react';
import EarlyHumansShapes from './EarlyHumansShapes';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    circle: ({ children, ...props }: any) => <circle {...props}>{children}</circle>,
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  useAnimation: () => ({
    start: jest.fn(),
    stop: jest.fn(),
  }),
}));

// Mock timers to prevent infinite loops in tests
jest.useFakeTimers();

describe('EarlyHumansShapes', () => {
  it('renders the container', () => {
    render(<EarlyHumansShapes progress={0.1} />);
    expect(screen.getByTestId('early-humans-container')).toBeInTheDocument();
  });

  it('does not show fire particles before ignition progress (36%)', () => {
    render(<EarlyHumansShapes progress={0.3} />);
    const debugInfo = screen.getByTestId('fire-debug');
    expect(debugInfo).toHaveAttribute('data-show-fire', 'false');
  });

  it('shows fire particles after ignition progress (36%)', () => {
    render(<EarlyHumansShapes progress={0.5} />);
    const debugInfo = screen.getByTestId('fire-debug');
    expect(debugInfo).toHaveAttribute('data-show-fire', 'true');
  });

  it('displays correct progress information', () => {
    render(<EarlyHumansShapes progress={0.75} />);
    const debugInfo = screen.getByTestId('fire-debug');
    expect(debugInfo).toHaveAttribute('data-progress', '0.750');
    expect(debugInfo).toHaveAttribute('data-show-fire', 'true');
  });

  it('renders constellations (stars)', () => {
    render(<EarlyHumansShapes progress={0.1} />);
    const container = screen.getByTestId('early-humans-container');
    // Check that SVG elements are present (constellations)
    const svgElements = container.querySelectorAll('svg');
    expect(svgElements.length).toBeGreaterThan(0);
  });

  it('calculates fire ignition progress correctly at boundary', () => {
    // Test the boundary condition - exactly at threshold should be false
    const { rerender } = render(<EarlyHumansShapes progress={0.36} />);
    const debugInfo = screen.getByTestId('fire-debug');
    expect(debugInfo).toHaveAttribute('data-show-fire', 'false');

    // Just above the threshold should be true
    rerender(<EarlyHumansShapes progress={0.37} />);
    expect(debugInfo).toHaveAttribute('data-show-fire', 'true');
  });

  it('renders correct number of fire, smoke, and sparkle particles when fire is active', () => {
    render(<EarlyHumansShapes progress={0.5} />);
    const debugInfo = screen.getByTestId('fire-debug');
    expect(debugInfo).toHaveAttribute('data-fire-particles-count', '120');
    expect(debugInfo).toHaveAttribute('data-smoke-particles-count', '25');
    expect(debugInfo).toHaveAttribute('data-sparkle-particles-count', '30');
  });

  it('renders no particles when fire is not active', () => {
    render(<EarlyHumansShapes progress={0.2} />);
    const debugInfo = screen.getByTestId('fire-debug');
    expect(debugInfo).toHaveAttribute('data-fire-particles-count', '0');
    expect(debugInfo).toHaveAttribute('data-smoke-particles-count', '0');
    expect(debugInfo).toHaveAttribute('data-sparkle-particles-count', '0');
  });
});
