'use client';

import React, { useMemo, useRef } from 'react';

import { cn } from '@/lib/utils';

import { Section } from '@/types';

interface TimelineProps {
  currentTime: number;
  duration: number;
  sections: Section[];
  onSeek?: (time: number) => void;
}

interface ProcessedSegment {
  id: number;
  start: number;
  end: number;
  section: string;
  keySoundElements: string;
  visualCue: string;
  textCue: string;
  realWorldSpan?: string;
  durationMa?: number;
  barWidthPercent?: number;
  // Calculated properties
  widthPercent: number;
  startPercent: number;
}

const TIMELINE_DURATION = 600; // 10 minutes in seconds

const formatSpan = (span: string | undefined) => {
  if (!span || !span.includes('→')) {
    // Return the original string if it's not a span, or an empty string if undefined.
    return span || '';
  }

  const parts = span.split('→');
  const startTime = parts[0].trim(); // e.g., "541" or "4.30 Ga"

  // To get the unit, we look at the second part of the span.
  const endTimeAndUnit = parts[1].trim(); // e.g., "485 Ma"

  // Find where the unit (the first letter) begins.
  const unitIndex = endTimeAndUnit.search(/[a-zA-Z]/);
  if (unitIndex === -1) {
    return startTime; // No unit found, just return the start time.
  }

  const unit = endTimeAndUnit.substring(unitIndex); // e.g., "Ma" or "CE"

  // If the start time already includes the unit (like in "4.30 Ga"), just return it.
  if (startTime.endsWith(unit)) {
    return startTime;
  }

  // Otherwise, combine the start time and the unit.
  return `${startTime} ${unit}`;
};

const Timeline: React.FC<TimelineProps> = ({ currentTime, duration, sections, onSeek }) => {
  // Pre-process segments to calculate widths and cumulative positions once.
  // This is more reliable than calculating layout properties during render.
  const processedSegments: ProcessedSegment[] = useMemo(() => {
    const segmentsWithWidth = sections.map(section => {
      let widthPercent = 0;
      if (section.barWidthPercent) {
        // Geological eras take up the first 80% of the bar
        widthPercent = section.barWidthPercent * 0.8;
      } else if (section.id === 12) {
        // Choice Corridor takes up 10%
        widthPercent = 10;
      } else if (section.id === 13) {
        // Future takes up the final 10%
        widthPercent = 10;
      }
      return { ...section, widthPercent };
    });

    let cumulativeWidth = 0;
    return segmentsWithWidth.map(section => {
      const startPercent = cumulativeWidth;
      cumulativeWidth += section.widthPercent;
      return { ...section, startPercent };
    });
  }, []);

  const timelineRef = useRef<HTMLDivElement>(null);

  const handleTimelineClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!onSeek || !timelineRef.current || duration === 0) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const timelineWidth = rect.width;
    const seekRatio = clickX / timelineWidth;
    const seekTime = seekRatio * duration;

    onSeek(seekTime);
  };

  const progressPercent = useMemo(() => {
    const currentSegment = processedSegments.find(
      (s) => currentTime >= s.start && currentTime < s.end
    );

    if (!currentSegment) {
      if (currentTime <= 0) return 0;
      if (currentTime >= TIMELINE_DURATION) return 100;
      // Fallback for any gaps, though data should be contiguous.
      return (currentTime / TIMELINE_DURATION) * 100;
    }

    const segmentDuration = currentSegment.end - currentSegment.start;
    if (segmentDuration <= 0) return currentSegment.startPercent;

    // Progress within the current segment's time (0 to 1)
    const progressInSegmentTime = (currentTime - currentSegment.start) / segmentDuration;

    // Map time progress to the segment's visual width
    const progressInSegmentVisual = progressInSegmentTime * currentSegment.widthPercent;

    return currentSegment.startPercent + progressInSegmentVisual;
  }, [currentTime, processedSegments]);

  return (
    // Add padding-top to make space for the absolutely positioned markers
    <div className="relative w-full pt-8">
      {/* Timestamp Markers positioned absolutely within the padded space */}
      {processedSegments.map((section, index) => {
        // No marker for the very first segment
        if (index === 0) return null;

        let label: string | null = null;
        if (section.id === 12) {
          label = 'Now';
        } else if (section.id === 13) {
          label = '?';
        } else {
          label = formatSpan(section.realWorldSpan);
        }

        // Do not render a marker if there is no label.
        if (!label) return null;

        return (
          <div
            key={`marker-${section.id}`}
            className="absolute top-0 flex flex-col items-center pointer-events-none"
            style={{
              left: `${section.startPercent}%`,
              transform: 'translate(-50%, 0)',
            }}
          >
            <span className="text-xs text-gray-400 whitespace-nowrap pb-1">
              {label}
            </span>
            <div className="w-px h-2 bg-white/50" />
          </div>
        );
      })}

      {/* Main timeline bar container */}
      <div 
        ref={timelineRef} 
        onClick={handleTimelineClick}
        className={`relative w-full h-8 rounded-full p-px bg-gradient-to-b from-gray-300/25 to-gray-600/20 ${onSeek ? 'cursor-pointer' : ''}`}
      >
        <div className="relative w-full h-full bg-black/40 rounded-full backdrop-blur-lg flex items-center overflow-hidden">
          <div className="w-full h-full flex rounded-full overflow-hidden">
            {processedSegments.map((section, index) => (
              <div
                key={section.id}
                className={cn(
                  'h-full flex items-center justify-center text-xs text-white/70 font-medium relative group',
                  index < processedSegments.length - 1 &&
                    "after:content-[''] after:absolute after:top-0 after:right-0 after:w-px after:h-full after:bg-white/50"
                )}
                style={{ width: `${section.widthPercent}%` }}
              >
                <div className="absolute inset-0 bg-black/10" />
                <span className="z-10 truncate px-2">{section.section}</span>
                <div className="absolute bottom-full mb-2 w-max px-3 py-1.5 bg-black/80 text-white text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-30">
                  <p className="font-bold">{section.section}</p>
                  {section.realWorldSpan && <p>Span: {section.realWorldSpan}</p>}
                  {section.durationMa && <p>Duration: {section.durationMa} Million Years</p>}
                  <div className="absolute top-full left-1/2 -translate-x-1/2 w-0 h-0 border-x-4 border-x-transparent border-t-4 border-t-black/80" />
                </div>
              </div>
            ))}
          </div>

          {/* "NOW" Marker Line (inside overflow container) */}
          <div
            data-testid="now-marker"
            className="absolute top-0 h-full w-4 z-20 transition-all duration-100 ease-linear"
            style={{ left: `calc(${progressPercent}% - 16px)` }} // Position container so its right edge is at progressPercent
          >
            <div className="relative w-full h-full">
              {/* Glow effect using a gradient */}
              <div className="absolute right-px h-full w-3 bg-gradient-to-l from-white/20 to-transparent" />
              {/* The 1px line */}
              <div className="absolute right-0 h-full w-px bg-gray-300/75" />
            </div>
          </div>
        </div>

        {/* The "NOW" label, centered above the line */}
        <div
          className="absolute -top-8 px-2 py-0.5 bg-black/40 text-white text-xs font-bold rounded-md backdrop-blur-sm border border-white/30 z-30 transition-all duration-100 ease-linear pointer-events-none"
          style={{
            left: `${progressPercent}%`,
            transform: 'translateX(-50%)',
          }}
        >
          NOW
        </div>
      </div>
    </div>
  );
};

export default Timeline;
