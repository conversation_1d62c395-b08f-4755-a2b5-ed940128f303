'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface PrimordialSoupShapesProps {
  progress: number;
}

const PrimordialSoupShapes: React.FC<PrimordialSoupShapesProps> = ({ progress }) => {
  // Use progress to control opacity, starting from dark and getting lighter.
  // Opacity can range from 0.05 to 0.2 for a subtle effect.
  // Tuned for better visibility and subtlety
  const adjustedProgress = Math.max(0, Math.min(1, (progress - 0.05) / 0.95));

  const opacity = 0.1 + adjustedProgress * 0.25;

  const shapeVariants = [
    {
      d: "M-100,300 Q-50,200 100,300 T400,300 T700,300 T1000,300 T1300,300 T1600,300 T1900,300 T2200,300",
      duration: 80,
      y: "20%",
    },
    {
      d: "M-100,500 Q0,600 200,500 T500,500 T800,500 T1100,500 T1400,500 T1700,500 T2000,500 T2300,500",
      duration: 110,
      y: "50%",
    },
    {
      d: "M-100,800 Q50,700 300,800 T600,800 T900,800 T1200,800 T1500,800 T1800,800 T2100,800 T2400,800",
      duration: 65,
      y: "80%",
    },
  ];

  return (
    <motion.svg
      width="100%"
      height="100%"
      className="absolute inset-0"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1, transition: { duration: 12 } }}
      exit={{ opacity: 0, transition: { duration: 12 } }}
    >
      <defs>
        <filter id="blur-filter">
          <feGaussianBlur in="SourceGraphic" stdDeviation="15" />
        </filter>
      </defs>
      {shapeVariants.map((shape, index) => (
        <motion.path
          key={index}
          d={shape.d}
          fill="none"
          stroke="rgba(200, 200, 240, 0.7)" // Tuned for visibility
          strokeWidth="3"
          style={{ y: shape.y, opacity }}
          filter="url(#blur-filter)"
          animate={{
            x: ['-100%', '100%'],
          }}
          transition={{
            x: {
              duration: shape.duration,
              repeat: Infinity,
              repeatType: 'loop',
              ease: 'linear',
            },
          }}
        />
      ))}
    </motion.svg>
  );
};

export default PrimordialSoupShapes;
