'use client';

import React, { useState, useEffect } from 'react';
import ImpactAndAftermathShapes from '@/components/animations/ImpactAndAftermathShapes';

export default function TestImpactPage() {
  const [progress, setProgress] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setProgress((prev) => {
        const newProgress = prev + 0.005; // Slower progression to see transitions
        return newProgress > 1 ? 0 : newProgress;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [isPlaying]);

  // Calculate opacity values to display
  const impactOpacity = progress <= 0.3 ? 1 : progress >= 0.6 ? 0 : (0.6 - progress) / 0.3;
  const ashOpacity = progress <= 0.2 ? 0 : progress >= 0.5 ? 1 : (progress - 0.2) / 0.3;

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Control Panel */}
      <div className="absolute top-4 left-4 z-50 bg-white/10 backdrop-blur-sm rounded-lg p-4 text-white max-w-sm">
        <h1 className="text-xl font-bold mb-4">Impact & Aftermath Test</h1>
        
        <div className="space-y-2 mb-4 text-sm">
          <div>Progress: {(progress * 100).toFixed(1)}%</div>
          <div>Impact Opacity: {impactOpacity.toFixed(3)}</div>
          <div>Ash Opacity: {ashOpacity.toFixed(3)}</div>
          <div className="mt-2 text-xs text-gray-300">
            <div>• Impact visible: 0-30% (full) → 30-60% (fade) → 60%+ (hidden)</div>
            <div>• Ash visible: 20-50% (fade in) → 50%+ (full)</div>
          </div>
        </div>

        <div className="space-y-2">
          <button
            onClick={() => setIsPlaying(!isPlaying)}
            className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-white mr-2"
          >
            {isPlaying ? 'Pause' : 'Play'} Animation
          </button>
          
          <div className="grid grid-cols-2 gap-2 mt-2">
            <button
              onClick={() => setProgress(0.1)}
              className="bg-orange-600 hover:bg-orange-700 px-3 py-1 rounded text-white text-sm"
            >
              Early (10%)
            </button>
            
            <button
              onClick={() => setProgress(0.4)}
              className="bg-yellow-600 hover:bg-yellow-700 px-3 py-1 rounded text-white text-sm"
            >
              Mid (40%)
            </button>
            
            <button
              onClick={() => setProgress(0.7)}
              className="bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-white text-sm"
            >
              Late (70%)
            </button>
            
            <button
              onClick={() => setProgress(1.0)}
              className="bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-white text-sm"
            >
              Fadeout (100%)
            </button>
          </div>
        </div>

        <div className="mt-4">
          <label className="block text-sm mb-2">Manual Progress Control:</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={progress}
            onChange={(e) => setProgress(parseFloat(e.target.value))}
            className="w-full"
          />
        </div>
      </div>

      {/* Impact & Aftermath Animation */}
      <ImpactAndAftermathShapes progress={progress} />

      {/* Visual Indicators */}
      <div className="absolute bottom-4 right-4 z-50 bg-white/10 backdrop-blur-sm rounded-lg p-4 text-white max-w-sm">
        <h2 className="font-bold mb-2">Expected Behavior:</h2>
        <ul className="text-sm space-y-1">
          <li className={impactOpacity > 0 ? 'text-green-400' : 'text-gray-500'}>
            • Wind streaks and rock explosions
          </li>
          <li className={ashOpacity > 0 ? 'text-green-400' : 'text-gray-500'}>
            • Ash rain particles
          </li>
          <li className="text-yellow-400 mt-2">
            • No sudden appearance during fadeout
          </li>
          <li className="text-yellow-400">
            • Smooth transitions between phases
          </li>
        </ul>
        
        <div className="mt-3 text-xs text-gray-300">
          <div className="font-semibold">Bug Fix Test:</div>
          <div>At 100% progress, impact effects should be completely hidden to prevent visual artifacts during era transition.</div>
        </div>
      </div>
    </div>
  );
}
