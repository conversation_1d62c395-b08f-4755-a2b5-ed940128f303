'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

interface VoteCounts {
  utopia: number;
  dystopia: number;
}

const ResultsPage = () => {
  const [votes, setVotes] = useState<VoteCounts | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVotes = async () => {
      try {
        const response = await fetch('/api/votes');
        if (!response.ok) {
          throw new Error('Failed to fetch vote counts.');
        }
        const data: VoteCounts = await response.json();
        setVotes(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchVotes();
  }, []);

  const totalVotes = votes ? votes.utopia + votes.dystopia : 0;
  const utopiaPercentage = totalVotes > 0 ? ((votes!.utopia / totalVotes) * 100).toFixed(1) : 0;
  const dystopiaPercentage = totalVotes > 0 ? ((votes!.dystopia / totalVotes) * 100).toFixed(1) : 0;

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-12 bg-black text-white">
      <div className="absolute inset-0 -z-10 animated-gradient-bg" />
      <div className="text-center max-w-2xl">
        <h1 className="text-5xl font-bold mb-6">The Collective Echo</h1>
        <p className="text-xl text-gray-400 mb-10">The timeline has settled. Here is the path most chosen.</p>

        {isLoading && <p className="text-lg">Loading results...</p>}
        {error && <p className="text-lg text-red-500">Error: {error}</p>}

        {votes && (
          <div className="w-full bg-gray-800/50 p-8 rounded-lg backdrop-blur-md border border-gray-700">
            <div className="flex justify-between items-end mb-4">
              <div className="text-left">
                <h2 className="text-3xl font-bold text-sky-400">Utopia</h2>
                <p className="text-lg">{votes.utopia.toLocaleString()} Voices</p>
              </div>
              <p className="text-4xl font-bold">{utopiaPercentage}%</p>
            </div>

            <div className="w-full bg-gray-700 rounded-full h-4 mb-8">
              <div
                className="bg-gradient-to-r from-sky-500 to-cyan-400 h-4 rounded-full"
                style={{ width: `${utopiaPercentage}%` }}
              />
            </div>

            <div className="flex justify-between items-end mb-4">
              <div className="text-left">
                <h2 className="text-3xl font-bold text-red-500">Dystopia</h2>
                <p className="text-lg">{votes.dystopia.toLocaleString()} Voices</p>
              </div>
              <p className="text-4xl font-bold">{dystopiaPercentage}%</p>
            </div>

            <div className="w-full bg-gray-700 rounded-full h-4">
              <div
                className="bg-gradient-to-r from-red-600 to-orange-500 h-4 rounded-full"
                style={{ width: `${dystopiaPercentage}%` }}
              />
            </div>
          </div>
        )}

        <Link href="/" passHref className="mt-12">
          <Button size="lg" variant="outline">Begin Anew</Button>
        </Link>
      </div>
    </main>
  );
};

export default ResultsPage;
