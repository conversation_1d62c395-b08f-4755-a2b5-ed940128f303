'use client';

import React, { useState, useEffect } from 'react';
import EarlyHumansShapes from '@/components/animations/EarlyHumansShapes';

export default function TestFirePage() {
  const [progress, setProgress] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setProgress((prev) => {
        const newProgress = prev + 0.01;
        return newProgress > 1 ? 0 : newProgress;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [isPlaying]);

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Control Panel */}
      <div className="absolute top-4 left-4 z-50 bg-white/10 backdrop-blur-sm rounded-lg p-4 text-white">
        <h1 className="text-xl font-bold mb-4">Fire Particles Test</h1>

        <div className="space-y-2 mb-4">
          <div>Progress: {(progress * 100).toFixed(1)}%</div>
          <div>Fire Active: {progress > 0.36 ? 'YES' : 'NO'}</div>
          <div>Fire Threshold: 36%</div>
        </div>

        <div className="space-y-2">
          <button
            onClick={() => setIsPlaying(!isPlaying)}
            className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-white mr-2"
          >
            {isPlaying ? 'Pause' : 'Play'} Animation
          </button>

          <button
            onClick={() => setProgress(0.2)}
            className="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded text-white mr-2"
          >
            Before Fire (20%)
          </button>

          <button
            onClick={() => setProgress(0.5)}
            className="bg-orange-600 hover:bg-orange-700 px-4 py-2 rounded text-white"
          >
            Fire Active (50%)
          </button>
        </div>

        <div className="mt-4">
          <label className="block text-sm mb-2">Manual Progress Control:</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={progress}
            onChange={(e) => setProgress(parseFloat(e.target.value))}
            className="w-full"
          />
        </div>
      </div>

      {/* Early Humans Animation */}
      <EarlyHumansShapes progress={progress} />

      {/* Visual Indicators */}
      <div className="absolute bottom-4 right-4 z-50 bg-white/10 backdrop-blur-sm rounded-lg p-4 text-white">
        <h2 className="font-bold mb-2">Expected Effects:</h2>
        <ul className="text-sm space-y-1">
          <li>• Stars twinkling in sky</li>
          <li>• Fire particles after 36% progress</li>
          <li>• 120 fire embers floating upward</li>
          <li>• 25 smoke particles</li>
          <li>• 30 sparkle particles</li>
          <li>• Particles distributed across screen</li>
          <li>• Realistic fire flickering background</li>
          <li>• Dynamic color temperature changes</li>
          <li>• Irregular intensity variations</li>
          <li>• Glowing visual effects</li>
        </ul>
      </div>
    </div>
  );
}
