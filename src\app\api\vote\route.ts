import { NextResponse } from 'next/server';
import { kv } from '@vercel/kv';

export async function POST(request: Request) {
  try {
    const { choice } = await request.json();

    if (choice !== 'utopia' && choice !== 'dystopia') {
      return NextResponse.json({ message: 'Invalid choice.' }, { status: 400 });
    }

    // Use Vercel KV to increment the vote count for the chosen path.
    const key = `soundscape:votes:${choice}`;
    await kv.incr(key);

    console.log(`Vote recorded for: ${choice}`);

    return NextResponse.json({ message: `Vote for ${choice} recorded successfully.` }, { status: 200 });
  } catch (error) {
    console.error('Error processing vote:', error);
    return NextResponse.json({ message: 'An error occurred while processing the vote.' }, { status: 500 });
  }
}
