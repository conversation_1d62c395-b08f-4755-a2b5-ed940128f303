import { test, expect } from '@playwright/test';

test('should navigate from landing to the experience page', async ({ page }) => {
  // Start from the index page (the baseURL is set in the config)
  await page.goto('/');

  // Check for the main heading on the landing page
  await expect(page.getByRole('heading', { name: 'Interactive Soundscape: A Journey Through Time' })).toBeVisible();

  // Find the "Begin the Journey" button and click it
  await page.getByRole('button', { name: /begin the journey/i }).click();

  // The new URL should be "/experience"
  await expect(page).toHaveURL('/experience');

  // The new page should contain a "Play" button.
  await expect(page.getByRole('button', { name: /play/i })).toBeVisible();
});
