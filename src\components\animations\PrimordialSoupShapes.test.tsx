import React from 'react';
import { render, screen } from '@testing-library/react';
import PrimordialSoupShapes from './PrimordialSoupShapes';

// Mock framer-motion to simplify testing
jest.mock('framer-motion', () => ({
  motion: {
    svg: React.forwardRef<SVGSVGElement, React.PropsWithChildren<{}>>((props, ref) => (
      <svg {...props} ref={ref}>
        {props.children}
      </svg>
    )),
    path: React.forwardRef<SVGPathElement, React.PropsWithChildren<{}>>((props, ref) => (
      <path {...props} ref={ref} />
    )),
  },
}));

describe('PrimordialSoupShapes', () => {
  it('renders the component without crashing', () => {
    const { container } = render(<PrimordialSoupShapes progress={0.5} />);
    const svgElement = container.querySelector('svg');
    expect(svgElement).toBeInTheDocument();
  });

  it('calculates and applies opacity based on progress', () => {
    const progress = 0.5;
    const { container } = render(<PrimordialSoupShapes progress={progress} />);

    // Calculate expected opacity based on the component's internal formula
    const adjustedProgress = Math.max(0, Math.min(1, (progress - 0.05) / 0.95));
    const expectedOpacity = 0.1 + adjustedProgress * 0.25;

    const paths = container.querySelectorAll('path');
    expect(paths.length).toBeGreaterThan(0);
    paths.forEach(path => {
      expect(path).toHaveStyle(`opacity: ${expectedOpacity}`);
    });
  });
});
