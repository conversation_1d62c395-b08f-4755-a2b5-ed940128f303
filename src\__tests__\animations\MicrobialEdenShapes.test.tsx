import React from 'react';
import { render, screen, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import MicrobialEdenShapes from '@/components/animations/MicrobialEdenShapes';

// Mock framer-motion to prevent actual animations
jest.mock('framer-motion', () => ({
  ...jest.requireActual('framer-motion'),
  animate: jest.fn(() => Promise.resolve()),
  motion: {
    ...jest.requireActual('framer-motion').motion,
    div: (props: React.ComponentProps<'div'>) => <div {...props} data-testid="cell" />,
  },
}));

describe('MicrobialEdenShapes', () => {
  let setIntervalCallback: () => void;
  let setIntervalSpy: jest.SpyInstance;
  let clearIntervalSpy: jest.SpyInstance;

  beforeEach(() => {
    // Mock timers manually to control the update loop
    setIntervalCallback = () => {};
    setIntervalSpy = jest.spyOn(window, 'setInterval').mockImplementation((callback) => {
      setIntervalCallback = callback as () => void;
      return 12345 as any;
    });
    clearIntervalSpy = jest.spyOn(window, 'clearInterval').mockImplementation(() => {});

    // Mock window size
    Object.defineProperty(window, 'innerWidth', { writable: true, configurable: true, value: 1024 });
    Object.defineProperty(window, 'innerHeight', { writable: true, configurable: true, value: 768 });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  const runTicks = (count: number) => {
    for (let i = 0; i < count; i++) {
      act(() => {
        setIntervalCallback();
      });
    }
  };

  it('should spawn cells up to the target count', () => {
    render(<MicrobialEdenShapes targetCellCount={10} />);
    runTicks(10);
    const cells = screen.getAllByTestId('cell');
    expect(cells.length).toBe(10);
  });

  it('should cull cells when target count decreases', () => {
    const { rerender } = render(<MicrobialEdenShapes targetCellCount={10} />);
    runTicks(10);
    expect(screen.getAllByTestId('cell').length).toBe(10);

    rerender(<MicrobialEdenShapes targetCellCount={5} />);
    runTicks(1);

    expect(screen.getAllByTestId('cell').length).toBe(5);
  });

  it('should call onEvolve when conditions are met', () => {
    const onEvolve = jest.fn();
    jest.spyOn(Math, 'random').mockReturnValue(0.04); // Ensure evolution happens

    render(<MicrobialEdenShapes targetCellCount={20} onEvolve={onEvolve} evolutionChance={0.1} />);
    runTicks(17); // After 16 ticks, we have 16 cells. On the 17th tick, the evolution condition (>15) is met.

    expect(onEvolve).toHaveBeenCalled();
    expect(screen.getAllByTestId('cell').length).toBe(15); // 1 cell evolves, 15 remain
  });

  it('should clean up the interval on unmount', () => {
    const { unmount } = render(<MicrobialEdenShapes targetCellCount={5} />);
    expect(setIntervalSpy).toHaveBeenCalledTimes(1);
    unmount();
    expect(clearIntervalSpy).toHaveBeenCalledWith(12345);
  });
});
