import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

interface EarlyHumansShapesProps {
  progress: number; // This will be used to time the fire ignition
}

// A single twinkling star
const Star: React.FC<{ cx: string; cy: string; size: number }> = ({ cx, cy, size }) => {
  return (
    <motion.circle
      cx={cx}
      cy={cy}
      r={size}
      fill="rgba(255, 255, 255, 0.8)"
      animate={{
        opacity: [0.6, 1, 0.6],
      }}
      transition={{
        duration: Math.random() * 2 + 2,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
    />
  );
};

// A simple constellation
const Constellation: React.FC<{ stars: { cx: string; cy: string; size: number }[] }> = ({ stars }) => {
  return (
    <g>
      {stars.map((star, i) => (
        <Star key={i} {...star} />
      ))}
    </g>
  );
};

// Enhanced fire particle system with realistic ember behavior
const FireParticles: React.FC = () => {
  const particles = useMemo(() => {
    const fireColors = ['#ffbe0b', '#fb5607', '#ff4500', '#ff6b35', '#ffa500', '#ff8c00', '#ff7f00'];
    return Array.from({ length: 120 }).map((_, i) => {
      const color = fireColors[Math.floor(Math.random() * fireColors.length)];
      const startX = Math.random() * 100; // Distribute across full width
      const startY = Math.random() * 100; // Distribute across entire screen height (0-100%)
      const horizontalDrift = (Math.random() - 0.5) * 30; // Random horizontal movement

      return {
        id: i,
        x: startX,
        y: startY,
        endX: startX + horizontalDrift, // Add horizontal drift for realism
        size: Math.random() * 3 + 1.5, // Particle size 1.5-4.5px
        duration: Math.random() * 6 + 5, // Longer duration for better visibility
        delay: Math.random() * 8, // Stagger particle appearance
        color,
        opacity: Math.random() * 0.4 + 0.6, // Vary base opacity (0.6-1.0)
      };
    });
  }, []);

  return (
    <svg className="absolute inset-0 w-full h-full z-40 pointer-events-none overflow-hidden" preserveAspectRatio="none" style={{ contain: 'layout style paint' }}>
      {particles.map(({ id, x, y, endX, size, duration, delay, color, opacity }) => (
        <motion.circle
          key={id}
          cx={`${x}%`}
          cy={`${y}%`}
          r={size}
          fill={color}
          initial={{
            x: `${x}%`,
            y: `${y}%`,
            opacity: 0,
            scale: 0.3
          }}
          animate={{
            x: `${endX}%`, // Horizontal drift
            y: `${y - 110}%`, // Float upward relative to starting position
            opacity: [0, opacity, opacity * 0.8, 0], // Fade in, sustain, fade out
            scale: [0.3, 1.2, 0.8, 0.2], // Dynamic scaling for ember effect
          }}
          transition={{
            duration,
            delay,
            repeat: Infinity,
            ease: 'easeOut', // More natural floating motion
          }}
          style={{
            filter: `blur(0.5px) drop-shadow(0 0 8px ${color}) drop-shadow(0 0 16px ${color}40)`,
          }}
        />
      ))}
    </svg>
  );
};

// Sparkle effects for extra visual appeal
const SparkleParticles: React.FC = () => {
  const sparkles = useMemo(() => {
    return Array.from({ length: 30 }).map((_, i) => {
      const startX = Math.random() * 100;
      const startY = Math.random() * 100; // Distribute sparkles across entire screen

      return {
        id: i,
        x: startX,
        y: startY,
        size: Math.random() * 1.5 + 0.5, // Small sparkles

        delay: Math.random() * 10,
        twinkleSpeed: Math.random() * 2 + 1,
      };
    });
  }, []);

  return (
    <svg className="absolute inset-0 w-full h-full z-45 pointer-events-none overflow-hidden" preserveAspectRatio="none" style={{ contain: 'layout style paint' }}>
      {sparkles.map(({ id, x, y, size, delay, twinkleSpeed }) => (
        <motion.circle
          key={id}
          cx={`${x}%`}
          cy={`${y}%`}
          r={size}
          fill="#ffffff"
          initial={{ opacity: 0, scale: 0 }}
          animate={{
            opacity: [0, 1, 0],
            scale: [0, 1.5, 0],
          }}
          transition={{
            duration: twinkleSpeed,
            delay,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          style={{
            filter: 'blur(0.5px) drop-shadow(0 0 4px #ffffff) drop-shadow(0 0 8px #ffff0080)',
          }}
        />
      ))}
    </svg>
  );
};

// Smoke particle system for added atmosphere
const SmokeParticles: React.FC = () => {
  const smokeParticles = useMemo(() => {
    const smokeColors = ['rgba(100, 100, 100, 0.3)', 'rgba(80, 80, 80, 0.2)', 'rgba(60, 60, 60, 0.1)'];
    return Array.from({ length: 25 }).map((_, i) => {
      const color = smokeColors[Math.floor(Math.random() * smokeColors.length)];
      const startX = Math.random() * 100; // Distribute smoke across full width
      const startY = Math.random() * 100; // Distribute smoke across entire screen height
      const horizontalDrift = (Math.random() - 0.5) * 40;

      return {
        id: i,
        x: startX,
        y: startY,
        endX: startX + horizontalDrift,
        size: Math.random() * 8 + 4, // Larger smoke particles
        duration: Math.random() * 8 + 6,
        delay: Math.random() * 10,
        color,
      };
    });
  }, []);

  return (
    <svg className="absolute inset-0 w-full h-full z-35 pointer-events-none overflow-hidden" preserveAspectRatio="none" style={{ contain: 'layout style paint' }}>
      {smokeParticles.map(({ id, x, y, endX, size, duration, delay, color }) => (
        <motion.circle
          key={id}
          cx={`${x}%`}
          cy={`${y}%`}
          r={size}
          fill={color}
          initial={{
            x: `${x}%`,
            y: `${y}%`,
            opacity: 0,
            scale: 0.5
          }}
          animate={{
            x: `${endX}%`,
            y: `${y - 120}%`, // Rise upward relative to starting position
            opacity: [0, 0.6, 0.4, 0],
            scale: [0.5, 1.5, 2, 0.1], // Expand as it rises
          }}
          transition={{
            duration,
            delay,
            repeat: Infinity,
            ease: 'easeOut',
          }}
          style={{
            filter: 'blur(3px)',
          }}
        />
      ))}
    </svg>
  );
};

// Realistic fire flickering background effect
const FireFlickerBackground: React.FC<{ isActive: boolean }> = ({ isActive }) => {
  if (!isActive) return null;

  return (
    <div className="absolute inset-0 overflow-hidden" style={{ contain: 'layout style paint size' }}>
      {/* Primary fire flicker layer */}
      <motion.div
        className="absolute inset-0 z-5"
        animate={{
          opacity: [0.3, 0.8, 0.5, 0.9, 0.4],
          scale: [1, 1.02, 0.98, 1.03, 1],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        style={{
          background: 'radial-gradient(ellipse at center, #FF450020 0%, #FF8C0015 40%, transparent 70%)',
          filter: 'blur(20px)',
          transformOrigin: 'center center',
          willChange: 'transform, opacity',
        }}
      />

      {/* Secondary ambient glow layer */}
      <motion.div
        className="absolute inset-0 z-4"
        animate={{
          opacity: [0.2, 0.4, 0.3, 0.5, 0.2],
          scale: [1, 1.05, 1.02, 1.08, 1],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 0.5,
        }}
        style={{
          background: 'radial-gradient(circle at center, #FFD70015 0%, transparent 60%)',
          filter: 'blur(40px)',
          transformOrigin: 'center center',
          willChange: 'transform, opacity',
        }}
      />

      {/* Color temperature variations */}
      <motion.div
        className="absolute inset-0 z-3"
        animate={{
          opacity: [0, 0.3, 0.1, 0.4, 0],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 1,
        }}
        style={{
          background: 'linear-gradient(45deg, #8B000008 0%, transparent 50%, #FFA50008 100%)',
          filter: 'blur(60px)',
          willChange: 'opacity',
        }}
      />
    </div>
  );
};

const EarlyHumansShapes: React.FC<EarlyHumansShapesProps> = ({ progress }) => {
  // Fire should start after the first stage (18s / 50s total duration for the era)
  // First stage duration is 18 seconds out of 50 total seconds
  const fireIgnitionProgress = 18 / 50; // 0.36 (36%)
  const showFire = progress > fireIgnitionProgress;



  const constellations = useMemo(() => [
    { stars: [{ cx: '20%', cy: '15%', size: 0.3 }, { cx: '25%', cy: '12%', size: 0.4 }, { cx: '28%', cy: '18%', size: 0.2 }] },
    { stars: [{ cx: '70%', cy: '20%', size: 0.3 }, { cx: '75%', cy: '25%', size: 0.2 }, { cx: '80%', cy: '22%', size: 0.4 }] },
    { stars: [{ cx: '50%', cy: '30%', size: 0.2 }, { cx: '48%', cy: '35%', size: 0.3 }] },
  ], []);

  return (
    <div className="absolute inset-0 overflow-hidden" data-testid="early-humans-container" style={{ contain: 'layout style paint' }}>
      {/* Fire Flickering Background - Behind everything */}
      <FireFlickerBackground isActive={showFire} />

      {/* Dark Sky Gradient */}
      <div
        className="absolute top-0 left-0 w-full h-1/2 z-10"
        style={{
          background: 'linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%)',
        }}
      />

      {/* Constellations */}
      <svg className="absolute inset-0 w-full h-full overflow-visible z-20">
        {constellations.map((c, i) => <Constellation key={i} stars={c.stars} />)}
      </svg>

      {/* Fire Animation */}
      {showFire && (
        <>
          {/* Enhanced Fire Base Glow */}
          {/* <div
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 z-25"
            style={{
              width: '300px',
              height: '150px',
              background: 'radial-gradient(ellipse at center bottom, rgba(255, 100, 0, 0.4) 0%, rgba(255, 150, 0, 0.3) 30%, rgba(255, 200, 0, 0.1) 60%, transparent 80%)',
              filter: 'blur(15px)',
            }} */}
          
          {/* Additional ambient glow */}
          <div
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 z-24"
            style={{
              width: '500px',
              height: '200px',
              background: 'radial-gradient(ellipse at center bottom, rgba(255, 80, 0, 0.1) 0%, rgba(255, 120, 0, 0.05) 50%, transparent 80%)',
              filter: 'blur(25px)',
            }}
          />
          <SmokeParticles />
          <FireParticles />
          <SparkleParticles />
        </>
      )}

      {/* Debug Info (for testing) */}
      <div
        className="hidden"
        data-testid="fire-debug"
        data-progress={progress.toFixed(3)}
        data-show-fire={showFire}
        data-fire-particles-count={showFire ? 120 : 0}
        data-smoke-particles-count={showFire ? 25 : 0}
        data-sparkle-particles-count={showFire ? 30 : 0}
      />
    </div>
  );
};

export default EarlyHumansShapes;
