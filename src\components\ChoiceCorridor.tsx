'use client';

import React from 'react';
import { Button } from './ui/button';

interface ChoiceCorridorProps {
  onSelectUtopia: () => void;
  onSelectDystopia: () => void;
}

const ChoiceCorridor: React.FC<ChoiceCorridorProps> = ({ onSelectUtopia, onSelectDystopia }) => {
  return (
    <div className="absolute inset-0 bg-black/70 flex items-center justify-center z-10 animate-fade-in">
      <div className="text-center p-8 bg-gray-900/50 rounded-xl backdrop-blur-md border border-gray-700">
        <h2 className="text-3xl font-bold text-white mb-4">The Path Diverges</h2>
        <p className="text-lg text-gray-300 mb-8">Your choice will shape the future.</p>
        <div className="flex gap-x-6">
          <Button onClick={onSelectUtopia} size="lg" variant="outline" className="hover:bg-sky-400 hover:text-black">
            Choose Utopia
          </Button>
          <Button onClick={onSelectDystopia} size="lg" variant="destructive" className="hover:bg-red-400 hover:text-black">
            Choose Dystopia
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChoiceCorridor;
