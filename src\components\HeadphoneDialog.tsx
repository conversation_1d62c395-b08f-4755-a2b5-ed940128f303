'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Headphones } from 'lucide-react';

interface HeadphoneDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const HeadphoneDialog: React.FC<HeadphoneDialogProps> = ({ open, onOpenChange }) => {
  const router = useRouter();

  const handleContinue = () => {
    router.push('/experience');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] bg-black/80 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="flex items-center text-2xl">
            <Headphones className="mr-2 h-6 w-6 text-gray-300" />
            For the Best Experience
          </DialogTitle>
          <DialogDescription className="text-gray-400 pt-2">
            This is an immersive audio journey. We strongly recommend using headphones to fully experience the detailed soundscape.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            onClick={handleContinue}
            variant="outline"
            className="w-full bg-white/10 text-gray-200 border-white/20 hover:bg-white/20"
          >
            Continue to the Experience
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default HeadphoneDialog;
