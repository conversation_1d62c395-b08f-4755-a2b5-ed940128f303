import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ExperiencePage from './page';

// Mock the global fetch function
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
  })
) as jest.Mock;

import { useAudioPlayer } from '@/hooks/useAudioPlayer';

// Mock the next/navigation module
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn().mockReturnValue(null),
  }),
}));

// Mock the custom hook and child components
jest.mock('@/hooks/useAudioPlayer');

jest.mock('@/components/Timeline', () => {
  const MockTimeline = () => <div data-testid="timeline" />;
  MockTimeline.displayName = 'MockTimeline';
  return MockTimeline;
});

jest.mock('@/components/TimedTextDisplay', () => {
  const MockTimedTextDisplay = () => <div data-testid="timed-text-display" />;
  MockTimedTextDisplay.displayName = 'MockTimedTextDisplay';
  return MockTimedTextDisplay;
});

jest.mock('@/components/ChoiceCorridor', () => {
  const MockChoiceCorridor = jest.fn(({ onSelectUtopia, onSelectDystopia }) => (
    <div data-testid="choice-corridor">
      <button onClick={onSelectUtopia}>Choose Utopia</button>
      <button onClick={onSelectDystopia}>Choose Dystopia</button>
    </div>
  ));
  return Object.assign(MockChoiceCorridor, { displayName: 'MockChoiceCorridor' });
});

describe('ExperiencePage branching logic', () => {
  const mockPlay = jest.fn();
  const mockPause = jest.fn();
  const mockChangeSource = jest.fn();

  const setupMockHook = (currentTime: number) => {
    (useAudioPlayer as jest.Mock).mockReturnValue({
      isPlaying: true,
      currentTime,
      play: mockPlay,
      pause: mockPause,
      changeSource: mockChangeSource,
    });
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('does not show ChoiceCorridor outside the choice window', () => {
    setupMockHook(100); // Time is 100s
    render(<ExperiencePage />);
    expect(screen.queryByTestId('choice-corridor')).not.toBeInTheDocument();
  });

  it('shows ChoiceCorridor within the choice window', () => {
    setupMockHook(490); // Time is 490s (8:10)
    render(<ExperiencePage />);
    expect(screen.getByTestId('choice-corridor')).toBeInTheDocument();
  });

  it('handles Utopia choice', () => {
    setupMockHook(490);
    const { rerender } = render(<ExperiencePage />);

    fireEvent.click(screen.getByRole('button', { name: /choose utopia/i }));

    expect(mockChangeSource).toHaveBeenCalledWith('/audio/Timeline_Utopia.mp3');

    // After choice is made, the component should disappear
    // We simulate this by re-rendering with the same time, but the internal state `choiceMade` should now be true
    rerender(<ExperiencePage />);
    expect(screen.queryByTestId('choice-corridor')).not.toBeInTheDocument();
  });

  it('handles Dystopia choice without changing source if already active', () => {
    setupMockHook(490);
    render(<ExperiencePage />);

    fireEvent.click(screen.getByRole('button', { name: /choose dystopia/i }));

    // Dystopia is the default, so changeSource should NOT be called
    expect(mockChangeSource).not.toHaveBeenCalled();
  });

  it('randomizes choice if no selection is made by the end time', async () => {
    const CHOICE_END_TIME = 540; // 9:00
    // Mock Math.random to force a predictable choice ('utopia')
    const randomSpy = jest.spyOn(Math, 'random').mockReturnValue(0.4);

    // Set the time to be after the choice window
    setupMockHook(CHOICE_END_TIME + 1);
    render(<ExperiencePage />);

    // Wait for the randomization logic to fire
    await waitFor(() => {
      expect(mockChangeSource).toHaveBeenCalledWith(expect.stringContaining('Utopia'));
    });

    // Clean up the mock
    randomSpy.mockRestore();
  });
});
