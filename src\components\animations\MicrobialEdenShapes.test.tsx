import React from 'react';
import { render, screen, act } from '@testing-library/react';
import MicrobialEdenShapes from './MicrobialEdenShapes';

// Mock framer-motion for a stable test environment
jest.mock('framer-motion', () => ({
  motion: {
    div: React.forwardRef<HTMLDivElement, React.PropsWithChildren<{}>>((props, ref) => (
      <div {...props} ref={ref} data-testid="cell">
        {props.children}
      </div>
    )),
  },
  AnimatePresence: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  motionValue: () => ({ get: () => 0, set: () => {} }),
  animate: jest.fn().mockResolvedValue(undefined),
}));

describe('MicrobialEdenShapes', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders the component without crashing', () => {
    render(<MicrobialEdenShapes targetCellCount={0} />);
    expect(screen.queryByTestId('cell')).not.toBeInTheDocument();
  });

  it('creates cells over time to match targetCellCount', () => {
    render(<MicrobialEdenShapes targetCellCount={5} />);

    // Initially, no cells should be present
    expect(screen.queryAllByTestId('cell')).toHaveLength(0);

    // Advance time by one interval (50ms)
    act(() => {
      jest.advanceTimersByTime(50);
    });
    expect(screen.queryAllByTestId('cell')).toHaveLength(1);

    // Advance time by four more intervals (200ms)
    act(() => {
      jest.advanceTimersByTime(200);
    });
    expect(screen.queryAllByTestId('cell')).toHaveLength(5);

    // Should not create more cells than the target
    act(() => {
      jest.advanceTimersByTime(100);
    });
    expect(screen.queryAllByTestId('cell')).toHaveLength(5);
  });

  it('culls cells over time to match a lower targetCellCount', () => {
    const { rerender } = render(<MicrobialEdenShapes targetCellCount={5} />);

    // Populate the cells
    act(() => {
      jest.advanceTimersByTime(250);
    });
    expect(screen.queryAllByTestId('cell')).toHaveLength(5);

    // Re-render with a lower target
    rerender(<MicrobialEdenShapes targetCellCount={2} />);

    // Advance time by one interval
    act(() => {
      jest.advanceTimersByTime(50);
    });
    expect(screen.queryAllByTestId('cell')).toHaveLength(4);

    // Advance time until the target is met
    act(() => {
      jest.advanceTimersByTime(150);
    });
    expect(screen.queryAllByTestId('cell')).toHaveLength(2);
  });

  it('clears the interval on unmount', () => {
    const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
    const { unmount } = render(<MicrobialEdenShapes targetCellCount={5} />);
    
    unmount();
    
    expect(clearIntervalSpy).toHaveBeenCalledTimes(1);
    clearIntervalSpy.mockRestore();
  });
});
