import { render, screen } from '@testing-library/react';
import ResultsPage from './page';

// Mock the global fetch function
global.fetch = jest.fn();

describe('ResultsPage', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  it('displays a loading state initially', () => {
    (fetch as jest.Mock).mockImplementationOnce(() => new Promise(() => {})); // Never resolves
    render(<ResultsPage />);
    expect(screen.getByText('Loading results...')).toBeInTheDocument();
  });

  it('displays results correctly after a successful fetch', async () => {
    const mockVotes = { utopia: 75, dystopia: 25 };
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockVotes,
    });

    render(<ResultsPage />);

    // Use findBy* to wait for the element to appear
    expect(await screen.findByText('75.0%')).toBeInTheDocument();
    expect(screen.getByText('25.0%')).toBeInTheDocument();
    expect(screen.getByText('75 Voices')).toBeInTheDocument();
    expect(screen.getByText('25 Voices')).toBeInTheDocument();
  });

  it('displays an error message if the fetch fails', async () => {
    (fetch as jest.Mock).mockRejectedValueOnce(new Error('Failed to fetch'));

    render(<ResultsPage />);

    expect(await screen.findByText(/error/i)).toBeInTheDocument();
  });

  it('renders the Begin Anew button', async () => {
    // Provide a mock to ensure the component can finish its async operations
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ utopia: 0, dystopia: 0 }),
    });

    render(<ResultsPage />);

    // Use findByRole to wait for the button to appear after loading state
    expect(
      await screen.findByRole('button', { name: /begin anew/i })
    ).toBeInTheDocument();
  });
});
