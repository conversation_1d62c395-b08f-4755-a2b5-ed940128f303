import React from 'react';
import { render, screen } from '@testing-library/react';
import MammalDawnShapes from './MammalDawnShapes';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    path: ({ children, ...props }: any) => <path {...props}>{children}</path>,
  },
}));

describe('MammalDawnShapes', () => {
  it('renders the container with overflow hidden', () => {
    render(<MammalDawnShapes progress={0.5} />);
    const container = screen.getByTestId('mammal-dawn-container');
    expect(container).toBeInTheDocument();
    expect(container).toHaveClass('overflow-hidden');
  });

  it('renders SVG with proper clipping and no overflow-visible', () => {
    render(<MammalDawnShapes progress={0.5} />);
    const container = screen.getByTestId('mammal-dawn-container');
    
    // Check that SVG elements are present
    const svgElements = container.querySelectorAll('svg');
    expect(svgElements.length).toBeGreaterThan(0);
    
    // Check that SVG has proper viewBox
    const treeSvg = svgElements[0];
    expect(treeSvg).toHaveAttribute('viewBox', '0 0 100 100');
    expect(treeSvg).toHaveAttribute('preserveAspectRatio', 'xMidYMid meet');
  });

  it('includes clipPath for proper bounds clipping', () => {
    render(<MammalDawnShapes progress={0.5} />);
    const container = screen.getByTestId('mammal-dawn-container');
    
    // Check for clipPath definition
    const clipPath = container.querySelector('clipPath#tree-clip');
    expect(clipPath).toBeInTheDocument();
    
    // Check for clipping rect
    const clipRect = container.querySelector('clipPath#tree-clip rect');
    expect(clipRect).toBeInTheDocument();
    expect(clipRect).toHaveAttribute('x', '0');
    expect(clipRect).toHaveAttribute('y', '0');
    expect(clipRect).toHaveAttribute('width', '100');
    expect(clipRect).toHaveAttribute('height', '100');
  });

  it('applies clipPath to the tree group', () => {
    render(<MammalDawnShapes progress={0.5} />);
    const container = screen.getByTestId('mammal-dawn-container');
    
    // Check that the group element has clipPath applied
    const clippedGroup = container.querySelector('g[clip-path="url(#tree-clip)"]');
    expect(clippedGroup).toBeInTheDocument();
  });

  it('renders drifting particles with overflow hidden', () => {
    render(<MammalDawnShapes progress={0.5} />);
    const container = screen.getByTestId('mammal-dawn-container');
    
    // DriftingParticles should be in a container with overflow-hidden
    const particleContainers = container.querySelectorAll('.overflow-hidden');
    expect(particleContainers.length).toBeGreaterThanOrEqual(2); // Main container + tree container
  });

  it('does not have scale transform that could cause overflow', () => {
    render(<MammalDawnShapes progress={0.5} />);
    const container = screen.getByTestId('mammal-dawn-container');
    
    // Check that no child elements have problematic scale transforms
    const scaledElements = container.querySelectorAll('[style*="scale(1.2)"]');
    expect(scaledElements.length).toBe(0);
  });

  it('renders tree branches at different progress levels', () => {
    // Test with no progress
    const { rerender } = render(<MammalDawnShapes progress={0} />);
    let container = screen.getByTestId('mammal-dawn-container');
    let paths = container.querySelectorAll('path');
    const initialPathCount = paths.length;

    // Test with some progress
    rerender(<MammalDawnShapes progress={0.5} />);
    container = screen.getByTestId('mammal-dawn-container');
    paths = container.querySelectorAll('path');
    const midPathCount = paths.length;

    // Test with full progress
    rerender(<MammalDawnShapes progress={1} />);
    container = screen.getByTestId('mammal-dawn-container');
    paths = container.querySelectorAll('path');
    const fullPathCount = paths.length;

    // More progress should generally result in more paths (tree branches)
    expect(fullPathCount).toBeGreaterThanOrEqual(midPathCount);
    expect(midPathCount).toBeGreaterThanOrEqual(initialPathCount);
  });
});
