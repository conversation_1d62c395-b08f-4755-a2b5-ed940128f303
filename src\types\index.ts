export interface Section {
  id: number;
  start: number;
  end: number;
  section: string;
  timedTextKey?: string;
  keySoundElements: string;
  visualCue: string;
  textCue: string;
  realWorldSpan?: string;
  durationMa?: number;
  startPercent?: number;
  endPercent?: number;
  barWidthPercent?: number;
}

export type UserChoice = 'utopia' | 'dystopia' | 'none';

export interface Era {
  id: string;
  subEra?: 'utopia' | 'dystopia' | null;
}

interface TimedTextCard {
  on: number;
  off: number;
  text: string;
}

interface ChoiceCorridorText {
  utopia: TimedTextCard;
  dystopia: TimedTextCard;
}

interface BranchingFuturesText {
  utopia: TimedTextCard;
  dystopia: TimedTextCard;
}

export type TimedTextData = {
  primordialSoup: TimedTextCard[];
  microbialEden: TimedTextCard[];
  cambrianBurst: TimedTextCard[];
  firstStepsOnLand: TimedTextCard[];
  ageOfReptiles: TimedTextCard[];
  impactAndAftermath: TimedTextCard[];
  mammalDawn: TimedTextCard[];
  earlyHumans: TimedTextCard[];
  agricultureAndCities: TimedTextCard[];
  industrialPulse: TimedTextCard[];
  digitalAIPresent: TimedTextCard[];
  choiceCorridor: ChoiceCorridorText;
  branchingFutures: BranchingFuturesText;
};
