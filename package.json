{"name": "interactive-soundscape", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest --config jest.config.ts", "test:watch": "jest --watch"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@vercel/kv": "^3.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.19.1", "lucide-react": "^0.522.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@eslint/eslintrc": "^3", "@playwright/test": "^1.53.1", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "tailwindcss": "^4", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.4", "typescript": "^5"}}