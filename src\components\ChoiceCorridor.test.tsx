import { render, screen, fireEvent } from '@testing-library/react';
import ChoiceCorridor from './ChoiceCorridor';

describe('ChoiceCorridor', () => {
  const mockOnSelectUtopia = jest.fn();
  const mockOnSelectDystopia = jest.fn();

  beforeEach(() => {
    // Reset mocks before each test
    mockOnSelectUtopia.mockClear();
    mockOnSelectDystopia.mockClear();
  });

  it('renders the component with title, description, and buttons', () => {
    render(
      <ChoiceCorridor
        onSelectUtopia={mockOnSelectUtopia}
        onSelectDystopia={mockOnSelectDystopia}
      />
    );

    expect(screen.getByText('The Path Diverges')).toBeInTheDocument();
    expect(screen.getByText('Your choice will shape the future.')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /choose utopia/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /choose dystopia/i })).toBeInTheDocument();
  });

  it('calls the onSelectUtopia handler when the Utopia button is clicked', () => {
    render(
      <ChoiceCorridor
        onSelectUtopia={mockOnSelectUtopia}
        onSelectDystopia={mockOnSelectDystopia}
      />
    );

    fireEvent.click(screen.getByRole('button', { name: /choose utopia/i }));

    expect(mockOnSelectUtopia).toHaveBeenCalledTimes(1);
    expect(mockOnSelectDystopia).not.toHaveBeenCalled();
  });

  it('calls the onSelectDystopia handler when the Dystopia button is clicked', () => {
    render(
      <ChoiceCorridor
        onSelectUtopia={mockOnSelectUtopia}
        onSelectDystopia={mockOnSelectDystopia}
      />
    );

    fireEvent.click(screen.getByRole('button', { name: /choose dystopia/i }));

    expect(mockOnSelectDystopia).toHaveBeenCalledTimes(1);
    expect(mockOnSelectUtopia).not.toHaveBeenCalled();
  });
});
