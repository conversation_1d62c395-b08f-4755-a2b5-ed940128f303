import { test, expect } from '@playwright/test';

const CHOICE_START_TIME = 480; // 8:00
const EXPERIENCE_END_TIME = 600; // 10:00

test('should complete the full experience, make a choice, and see results', async ({ page }) => {
  // Start on the experience page
  await page.goto('/experience');

  // Click the play button to start the audio
  await page.getByRole('button', { name: /play/i }).click();

  // Wait for our custom seek function to be available on the window, then use it
  await page.waitForFunction(() => (window as any).seek);
  await page.evaluate((time) => (window as any).seek(time), CHOICE_START_TIME);

  // Wait for the choice buttons to appear
  const utopiaButton = page.getByRole('button', { name: /utopia/i });
  await expect(utopiaButton).toBeVisible({ timeout: 5000 });

  // Set up a route interceptor to catch the vote API call
  const votePromise = page.waitForRequest(req => 
    req.url().includes('/api/vote') && req.method() === 'POST'
  );

  // Click the Utopia button
  await utopiaButton.click();

  // Wait for the API call and verify its payload
  const voteRequest = await votePromise;
  const postData = voteRequest.postDataJSON();
  expect(postData).toEqual({ choice: 'utopia' });

  // Fast-forward to the exact end, explicitly setting duration for reliability
  await page.evaluate((args) => {
    (window as any).seek(args.time, args.duration);
  }, { time: EXPERIENCE_END_TIME, duration: EXPERIENCE_END_TIME });

  // Wait for the redirect to the results page
  await page.waitForURL('/results', { timeout: 5000 });

  // Verify the results page is displayed
  await expect(page.getByRole('heading', { name: 'The Collective Echo' })).toBeVisible();
});
