'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface FirstStepsOnLandShapesProps {
  progress: number;
}

const Fern: React.FC<{ d: string; progress: number; delay: number }> = ({ d, progress, delay }) => {
  const pathLength = Math.max(0, (progress - delay) / (1 - delay));
  return (
    <motion.path
      d={d}
      stroke="#2E8B57" // SeaGreen
      strokeWidth="2.5"
      fill="none"
      initial={{ pathLength: 0, opacity: 0 }}
      animate={{ 
        pathLength: pathLength > 0 ? pathLength : 0,
        opacity: pathLength > 0 ? 1 : 0
      }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    />
  );
};

const Amphibian: React.FC<{ progress: number; delay: number }> = ({ progress, delay }) => {
  const emergence = Math.max(0, (progress - delay) / (1 - delay));
  return (
    <motion.g
      initial={{ x: -100, y: 850, rotate: -15, opacity: 0 }}
      animate={{
        x: emergence > 0 ? -100 + emergence * 250 : -100,
        opacity: emergence > 0 ? 1 : 0
      }}
      transition={{ duration: 2, ease: 'linear' }}
    >
      <motion.ellipse cx="50" cy="10" rx="40" ry="12" fill="#556B2F" />
      <motion.circle cx="20" cy="20" r="5" fill="#556B2F" />
      <motion.circle cx="80" cy="20" r="5" fill="#556B2F" />
    </motion.g>
  );
};

const FirstStepsOnLandShapes: React.FC<FirstStepsOnLandShapesProps> = ({ progress }) => {
  const ferns = [
    { d: 'M100 1000 V 600 C 50 400 150 400 100 200', delay: 0 },
    { d: 'M300 1000 V 700 C 250 550 350 550 300 400', delay: 0.1 },
    { d: 'M500 1000 V 800 C 450 700 550 700 500 600', delay: 0.2 },
    { d: 'M700 1000 V 650 C 650 450 750 450 700 250', delay: 0.05 },
    { d: 'M900 1000 V 750 C 850 600 950 600 900 450', delay: 0.15 },
  ];

  return (
    <svg
      className="absolute inset-0 w-full h-full"
      viewBox="0 0 1000 1000"
      preserveAspectRatio="xMidYMid slice"
      aria-hidden="true"
    >
      <defs>
        <filter id="glow">
          <feGaussianBlur stdDeviation="3.5" result="coloredBlur" />
          <feMerge>
            <feMergeNode in="coloredBlur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>
      <g style={{ filter: 'url(#glow)' }}>
        {ferns.map((fern, index) => (
          <Fern key={index} {...fern} progress={progress} />
        ))}
        
        <Amphibian progress={progress} delay={0.4} />
        <motion.g transform="scale(-1, 1) translate(-1000, 0)">
          <Amphibian progress={progress} delay={0.6} />
        </motion.g>
      </g>
    </svg>
  );
};

export default FirstStepsOnLandShapes;
