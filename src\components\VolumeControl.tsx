'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Volume2, Volume1, VolumeX } from 'lucide-react';

import { cn } from '@/lib/utils';

interface VolumeControlProps {
  volume: number;
  setVolume: (volume: number) => void;
  className?: string;
}

const VolumeControl: React.FC<VolumeControlProps> = ({ volume, setVolume, className }) => {
  const [isSliderVisible, setIsSliderVisible] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setVolume(parseFloat(event.target.value));
  };

  const getVolumeIcon = () => {
    if (volume === 0) {
      return <VolumeX className="h-5 w-5" />;
    }
    if (volume < 0.5) {
      return <Volume1 className="h-5 w-5" />;
    }
    return <Volume2 className="h-5 w-5" />;
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isSliderVisible && containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsSliderVisible(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isSliderVisible]);

  return (
    <div
      ref={containerRef}
      className={cn(
        'relative rounded-md p-px bg-gradient-to-b from-gray-300/25 to-gray-600/20',
        className
      )}
    >
      <div className="flex items-center bg-black/50 backdrop-blur-sm rounded-[6px] h-10 transition-all duration-300 ease-in-out hover:bg-white/10">
        <button
          onClick={() => setIsSliderVisible(!isSliderVisible)}
          className="h-full px-3 flex items-center bg-transparent rounded-l-[6px] rounded-r-none transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 ring-offset-background"
          aria-label="Toggle volume control"
        >
          {getVolumeIcon()}
        </button>
        <div
          className={cn(
            'transition-all duration-300 ease-in-out flex items-center',
            isSliderVisible ? 'w-24 pr-4' : 'w-0'
          )}
        >
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={volume}
            onChange={handleVolumeChange}
            className={cn(
              'w-full h-2 bg-gray-700/50 rounded-lg appearance-none cursor-pointer slider-thumb transition-opacity',
              isSliderVisible ? 'opacity-100 duration-300' : 'opacity-0 duration-200'
            )}
            aria-label="Volume slider"
          />
        </div>
      </div>
    </div>
  );
};

export default VolumeControl;
