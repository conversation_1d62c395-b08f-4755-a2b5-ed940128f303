'use client';

import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { ArrowRight } from 'lucide-react';
import { motion, Variants } from 'framer-motion';
import HeadphoneDialog from '@/components/HeadphoneDialog';

export default function Home() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: 'easeOut' } },
  };

  return (
    <div className="relative min-h-screen w-full overflow-hidden bg-black">
      {/* Animated Gradient Background */}
      <div className="absolute inset-0 -z-10 animated-gradient-bg" />

      {/* Content */}
      <main className="flex min-h-screen flex-col items-center justify-center p-8 text-center text-white">
        <motion.div
          className="max-w-3xl"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.h1
            variants={itemVariants}
            className="text-5xl font-extrabold tracking-tighter sm:text-6xl md:text-7xl lg:text-8xl"
          >
            A Journey Through
            <span className="block text-gray-200 drop-shadow-[0_0_10px_rgba(255,255,255,0.2)]">
              4 Billion Years
            </span>
          </motion.h1>
          <motion.p
            variants={itemVariants}
            className="mt-6 max-w-xl mx-auto text-lg leading-8 text-gray-300"
          >
            Experience the entire history of Earth in a 10-minute interactive
            soundscape. Your choices will shape the future.
          </motion.p>
          <motion.div
            variants={itemVariants}
            className="mt-10 flex items-center justify-center gap-x-6"
          >
            <Button
              onClick={() => setIsDialogOpen(true)}
              size="lg"
              variant="outline"
              className="bg-black/20 text-lg font-semibold text-white backdrop-blur-sm transition-all hover:bg-white/20 hover:text-white"
            >
              Begin the Journey
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </motion.div>
        </motion.div>
      </main>
      <HeadphoneDialog open={isDialogOpen} onOpenChange={setIsDialogOpen} />
    </div>
  );
}

