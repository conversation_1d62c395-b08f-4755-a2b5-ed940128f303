import React from 'react';
import { render, screen, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import CambrianBurstShapes from '@/components/animations/CambrianBurstShapes';

// Mock framer-motion to control animations
jest.mock('framer-motion', () => {
  const original = jest.requireActual('framer-motion');
  return {
    ...original,
    animate: jest.fn(),
    motion: {
      ...original.motion,
      div: (props: any) => <div {...props} />,
    },
  };
});

describe('CambrianBurstShapes', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    // Mock window size
    Object.defineProperty(window, 'innerWidth', { writable: true, configurable: true, value: 1024 });
    Object.defineProperty(window, 'innerHeight', { writable: true, configurable: true, value: 768 });
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should spawn creatures from the evolution queue', () => {
    const onEvolutionComplete = jest.fn();
    const evolutionQueue = [
      { id: 1, x: 100, y: 100, size: 20 },
      { id: 2, x: 200, y: 200, size: 20 },
    ];

    const { rerender } = render(
      <CambrianBurstShapes evolutionQueue={[]} onEvolutionComplete={onEvolutionComplete} />
    );

    rerender(
      <CambrianBurstShapes evolutionQueue={evolutionQueue} onEvolutionComplete={onEvolutionComplete} />
    );

    // Should render two creatures from the queue
    const creatures = screen.getAllByTestId(/creature-/);
    expect(creatures).toHaveLength(2);

    // Should call the completion callback
    expect(onEvolutionComplete).toHaveBeenCalledTimes(1);
  });


});
