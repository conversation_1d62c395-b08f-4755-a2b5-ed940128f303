import React from 'react';
import { render, screen } from '@testing-library/react';
import ImpactAndAftermathShapes from './ImpactAndAftermathShapes';

describe('ImpactAndAftermathShapes', () => {
  it('renders the initial impact effects (shockwave and wind) at the beginning of the era', () => {
    render(<ImpactAndAftermathShapes progress={0.1} />);
    const container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();
    // A more robust test would check for the presence of rock and wind streak elements
  });

  it('renders the ash rain during the era', () => {
    render(<ImpactAndAftermathShapes progress={0.2} />);
    const container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();
    // A more robust test would check for the presence of ash particle elements
  });

  it('hides impact effects (wind/rocks) during the later part of the era', () => {
    render(<ImpactAndAftermathShapes progress={0.7} />);
    const container = screen.getByTestId('impact-container');
    // The container should still be there for the ash rain
    expect(container).toBeInTheDocument();
    // At 70% progress, wind/rocks should be hidden (they fade out by 60%)
  });

  it('shows impact effects during the early part of the era', () => {
    render(<ImpactAndAftermathShapes progress={0.4} />);
    const container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();
    // At 40% progress, wind/rocks should still be visible
  });

  it('properly transitions from impact effects to ash rain', () => {
    render(<ImpactAndAftermathShapes progress={0.5} />);
    const container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();
    // At 50% progress, we're in the transition period where impact effects are fading
    // and ash rain should be visible
  });
});
