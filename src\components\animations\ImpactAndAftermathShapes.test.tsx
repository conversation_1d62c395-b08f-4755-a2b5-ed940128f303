import React from 'react';
import { render, screen } from '@testing-library/react';
import ImpactAndAftermathShapes from './ImpactAndAftermathShapes';

describe('ImpactAndAftermathShapes', () => {
  it('renders the initial impact effects (shockwave and wind) at the beginning of the era', () => {
    render(<ImpactAndAftermathShapes progress={0.1} />);
    const container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();
    // A more robust test would check for the presence of rock and wind streak elements
  });

  it('renders the ash rain during the era', () => {
    render(<ImpactAndAftermathShapes progress={0.2} />);
    const container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();
    // A more robust test would check for the presence of ash particle elements
  });

  it('hides impact effects (wind/rocks) during the later part of the era', () => {
    render(<ImpactAndAftermathShapes progress={0.7} />);
    const container = screen.getByTestId('impact-container');
    // The container should still be there for the ash rain
    expect(container).toBeInTheDocument();
    // At 70% progress, wind/rocks should be hidden (they fade out by 60%)
  });

  it('shows impact effects during the early part of the era', () => {
    render(<ImpactAndAftermathShapes progress={0.4} />);
    const container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();
    // At 40% progress, wind/rocks should still be visible
  });

  it('properly transitions from impact effects to ash rain', () => {
    render(<ImpactAndAftermathShapes progress={0.5} />);
    const container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();
    // At 50% progress, we're in the transition period where impact effects are fading
    // and ash rain should be visible
  });

  it('ensures impact effects stay hidden during era fadeout transition', () => {
    // Test that impact effects are completely hidden at 100% progress (during era fadeout)
    render(<ImpactAndAftermathShapes progress={1.0} />);
    const container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();
    // At 100% progress, impact effects should be completely hidden to prevent visual artifacts
  });

  it('calculates opacity correctly at boundary conditions', () => {
    // Test the exact boundary conditions for impact opacity
    const { rerender } = render(<ImpactAndAftermathShapes progress={0.3} />);
    let container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();

    // At exactly 30% progress, impact should be fully visible
    rerender(<ImpactAndAftermathShapes progress={0.3} />);
    container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();

    // At exactly 60% progress, impact should be completely hidden
    rerender(<ImpactAndAftermathShapes progress={0.6} />);
    container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();

    // Beyond 60% progress, impact should remain hidden
    rerender(<ImpactAndAftermathShapes progress={0.8} />);
    container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();
  });
});
