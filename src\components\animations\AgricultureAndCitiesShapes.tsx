'use client';

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

interface AgricultureAndCitiesShapesProps {
  progress: number;
}

const AgricultureAndCitiesShapes: React.FC<AgricultureAndCitiesShapesProps> = ({ progress }) => {
  const gridLines = useMemo(() => {
    const lines = [];
    const density = 20; // Corresponds to 21 lines in each direction

    // Horizontal lines
    for (let i = 0; i <= density; i++) {
      lines.push({ 
        id: `h-${i}`,
        d: `M 0 ${i * (100 / density)} L 100 ${i * (100 / density)}`,
        delay: Math.random() * 2
      });
    }

    // Vertical lines
    for (let i = 0; i <= density; i++) {
      lines.push({ 
        id: `v-${i}`,
        d: `M ${i * (100 / density)} 0 L ${i * (100 / density)} 100`,
        delay: Math.random() * 2
      });
    }

    return lines;
  }, []);

  const visibleLines = useMemo(() => {
    const totalLines = gridLines.length;
    const count = Math.floor(progress * totalLines);
    return gridLines.slice(0, count);
  }, [progress, gridLines]);

  return (
    <div className="absolute inset-0 overflow-hidden" data-testid="agriculture-cities-container">
      <svg viewBox="0 0 100 100" className="w-full h-full" preserveAspectRatio="none">
        {visibleLines.map(({ id, d, delay }) => (
          <motion.path
            key={id}
            d={d}
            stroke="rgba(255, 255, 255, 0.3)"
            strokeWidth={0.1}
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 1 }}
            transition={{ duration: 2, delay, ease: 'easeInOut' }}
          />
        ))}
      </svg>
    </div>
  );
};

export default AgricultureAndCitiesShapes;
