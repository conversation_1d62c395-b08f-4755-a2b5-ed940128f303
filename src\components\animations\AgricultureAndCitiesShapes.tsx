'use client';

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

interface AgricultureAndCitiesShapesProps {
  progress: number;
}

// More earthy and historically inspired tones for settlements
const settlementColors = [
  '#A0522D', // Sienna
  '#D2691E', // Chocolate
  '#8B4513', // SaddleBrown
  '#BC8F8F', // RosyBrown
  '#CD853F', // Peru
];

// Softer, more organic path color
const pathColor = 'rgba(222, 184, 135, 0.4)'; // BurlyWood with opacity

// Helper to create a random, organic blob-like SVG path
const createBlobPath = (x: number, y: number, size: number): string => {
  const numPoints = 12;
  const angleStep = (Math.PI * 2) / numPoints;
  const irregularity = 0.5; // Increased irregularity for more organic shapes

  const points = Array.from({ length: numPoints }, (_, i) => {
    const angle = i * angleStep;
    const radius = size * (1 + (Math.random() - 0.5) * 2 * irregularity);
    return {
      x: x + Math.cos(angle) * radius,
      y: y + Math.sin(angle) * radius,
    };
  });

  let d = `M ${points[0].x} ${points[0].y}`;
  for (let i = 0; i < numPoints; i++) {
    const p1 = points[i];
    const p2 = points[(i + 1) % numPoints];
    const midX = (p1.x + p2.x) / 2;
    const midY = (p1.y + p2.y) / 2;
    d += ` Q ${p1.x},${p1.y} ${midX},${midY}`;
  }
  d += ' Z';
  return d;
};



type Settlement = { id: string; x: number; y: number; d: string; color: string };
type Path = { id: string; d: string };

const AgricultureAndCitiesShapes: React.FC<AgricultureAndCitiesShapesProps> = ({ progress }) => {
  const { settlements, paths } = useMemo(() => {
    const settlementList: Settlement[] = [];
    const pathList: Path[] = [];
    const totalSettlements = 40;

    // Create settlements with a tendency to cluster
    for (let i = 0; i < totalSettlements; i++) {
      let x, y;
      if (i > 0 && Math.random() > 0.3 && settlementList.length > 0) {
        // Spawn near an existing settlement
        const parent = settlementList[Math.floor(Math.random() * settlementList.length)];
        x = parent.x + (Math.random() - 0.5) * 30;
        y = parent.y + (Math.random() - 0.5) * 30;
      } else {
        // Spawn randomly
        x = Math.random() * 100;
        y = Math.random() * 100;
      }
      x = Math.max(5, Math.min(95, x)); // Clamp to view
      y = Math.max(5, Math.min(95, y));

      const size = 0.8 + Math.random() * 1.2;
      const color = settlementColors[Math.floor(Math.random() * settlementColors.length)];
      settlementList.push({
        id: `s-${i}`,
        x,
        y,
        d: createBlobPath(x, y, size),
        color,
      });
    }

    // Create paths ensuring each settlement has at most one connection to its closest neighbor
    const connectedSettlementIds = new Set<string>();
    const connectionThreshold = 30; // Max distance for a connection

    for (const s1 of settlementList) {
      if (connectedSettlementIds.has(s1.id)) {
        continue;
      }

      let closestPartner: Settlement | null = null;
      let minDistance = Infinity;

      for (const s2 of settlementList) {
        if (s1.id === s2.id || connectedSettlementIds.has(s2.id)) {
          continue;
        }

        const dist = Math.hypot(s1.x - s2.x, s1.y - s2.y);
        if (dist < minDistance) {
          minDistance = dist;
          closestPartner = s2;
        }
      }

      if (closestPartner && minDistance < connectionThreshold) {
        const p1 = { x: s1.x, y: s1.y };
        const p2 = { x: closestPartner.x, y: closestPartner.y };

        const midX = (p1.x + p2.x) / 2;
        const midY = (p1.y + p2.y) / 2;

        const vecX = p2.x - p1.x;
        const vecY = p2.y - p1.y;

        // Get a perpendicular vector
        const perpX = -vecY;
        const perpY = vecX;

        // Normalize the perpendicular vector
        const len = Math.hypot(perpX, perpY);
        const normPerpX = len === 0 ? 0 : perpX / len;
        const normPerpY = len === 0 ? 0 : perpY / len;

        // Randomize curve amount and direction for variety
        const curveAmount = (Math.random() * 15 + 10) * (Math.random() > 0.5 ? 1 : -1);

        const controlX = midX + normPerpX * curveAmount;
        const controlY = midY + normPerpY * curveAmount;

        pathList.push({
          id: `p-${s1.id}-${closestPartner.id}`,
          d: `M ${p1.x} ${p1.y} Q ${controlX} ${controlY} ${p2.x} ${p2.y}`,
        });
        connectedSettlementIds.add(s1.id);
        connectedSettlementIds.add(closestPartner.id);
      }
    }
    
    // Randomize order for organic appearance
    settlementList.sort(() => Math.random() - 0.5);
    pathList.sort(() => Math.random() - 0.5);

    return { settlements: settlementList, paths: pathList };
  }, []);

  const visibleSettlementsCount = Math.floor(progress * settlements.length);
  const pathProgress = Math.max(0, (progress - 0.1) / 0.9); // Paths start after 10% of settlements appear
  const visiblePathsCount = Math.floor(pathProgress * paths.length);

  return (
    <div className="absolute inset-0 overflow-hidden" data-testid="agriculture-cities-container">
      <svg viewBox="0 0 100 100" className="w-full h-full" preserveAspectRatio="none">
        <defs>
          <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="0.5" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>

        {/* Paths (Roads/Rivers) - Thicker and more organic */}
        {paths.slice(0, visiblePathsCount).map(({ id, d }, index) => (
          <motion.path
            key={id}
            d={d}
            stroke={pathColor}
            strokeWidth={0.35} // Increased thickness
            fill="none"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 1 }}
            transition={{ duration: 8, delay: index * 0.2, ease: 'easeInOut' }}
          />
        ))}

        {/* Settlements - Organic Shapes with Outlines and Glow */}
        {settlements.slice(0, visibleSettlementsCount).map(({ id, d, color }, index) => (
          <motion.path
            key={id}
            d={d}
            fill={`${color}99`} // Semi-transparent fill (~60%)
            stroke={color}
            strokeWidth={0.15}
            filter="url(#glow)"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{
              opacity: 1,
              scale: 1,
            }}
            transition={{
              duration: 2.5,
              delay: index * 0.1,
              ease: 'easeOut',
            }}
          />
        ))}
      </svg>
    </div>
  );
};

export default AgricultureAndCitiesShapes;
