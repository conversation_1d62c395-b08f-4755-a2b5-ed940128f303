'use client';

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

interface AgricultureAndCitiesShapesProps {
  progress: number;
}

// More earthy and historically inspired tones for settlements
const settlementColors = [
  '#A0522D', // Sienna
  '#D2691E', // Chocolate
  '#8B4513', // SaddleBrown
  '#BC8F8F', // RosyBrown
  '#CD853F', // Peru
];

// Softer, more organic path color
const pathColor = 'rgba(222, 184, 135, 0.4)'; // BurlyWood with opacity

// Helper to create a random, organic blob-like SVG path
const createBlobPath = (x: number, y: number, size: number): string => {
  const numPoints = 12;
  const angleStep = (Math.PI * 2) / numPoints;
  const irregularity = 0.5; // Increased irregularity for more organic shapes

  const points = Array.from({ length: numPoints }, (_, i) => {
    const angle = i * angleStep;
    const radius = size * (1 + (Math.random() - 0.5) * 2 * irregularity);
    return {
      x: x + Math.cos(angle) * radius,
      y: y + Math.sin(angle) * radius,
    };
  });

  let d = `M ${points[0].x} ${points[0].y}`;
  for (let i = 0; i < numPoints; i++) {
    const p1 = points[i];
    const p2 = points[(i + 1) % numPoints];
    const midX = (p1.x + p2.x) / 2;
    const midY = (p1.y + p2.y) / 2;
    d += ` Q ${p1.x},${p1.y} ${midX},${midY}`;
  }
  d += ' Z';
  return d;
};

// Helper to create a more natural, wandering path
const createWanderingPath = (x1: number, y1: number, x2: number, y2: number): string => {
  const segments = 10;
  const sway = 15; // How much the path deviates
  let d = `M ${x1} ${y1}`;
  for (let i = 1; i <= segments; i++) {
    const t = i / segments;
    const midX = x1 * (1 - t) + x2 * t + (Math.random() - 0.5) * sway * Math.sin(t * Math.PI);
    const midY = y1 * (1 - t) + y2 * t + (Math.random() - 0.5) * sway * Math.sin(t * Math.PI);
    d += ` L ${midX} ${midY}`;
  }
  return d;
};

type Settlement = { id: string; x: number; y: number; d: string; color: string };
type Path = { id: string; d: string };

const AgricultureAndCitiesShapes: React.FC<AgricultureAndCitiesShapesProps> = ({ progress }) => {
  const { settlements, paths } = useMemo(() => {
    const settlementList: Settlement[] = [];
    const pathList: Path[] = [];
    const totalSettlements = 40;

    // Create settlements with a tendency to cluster
    for (let i = 0; i < totalSettlements; i++) {
      let x, y;
      if (i > 0 && Math.random() > 0.3 && settlementList.length > 0) {
        // Spawn near an existing settlement
        const parent = settlementList[Math.floor(Math.random() * settlementList.length)];
        x = parent.x + (Math.random() - 0.5) * 30;
        y = parent.y + (Math.random() - 0.5) * 30;
      } else {
        // Spawn randomly
        x = Math.random() * 100;
        y = Math.random() * 100;
      }
      x = Math.max(5, Math.min(95, x)); // Clamp to view
      y = Math.max(5, Math.min(95, y));

      const size = 0.8 + Math.random() * 1.2;
      const color = settlementColors[Math.floor(Math.random() * settlementColors.length)];
      settlementList.push({
        id: `s-${i}`,
        x,
        y,
        d: createBlobPath(x, y, size),
        color,
      });
    }

    // Create paths between nearby settlements
    for (let i = 0; i < settlementList.length; i++) {
      for (let j = i + 1; j < settlementList.length; j++) {
        const s1 = settlementList[i];
        const s2 = settlementList[j];
        const dist = Math.hypot(s1.x - s2.x, s1.y - s2.y);
        if (dist < 25 && Math.random() > 0.5) { // Connect if close enough, with some randomness
          pathList.push({
            id: `p-${i}-${j}`,
            d: createWanderingPath(s1.x, s1.y, s2.x, s2.y),
          });
        }
      }
    }
    
    // Randomize order for organic appearance
    settlementList.sort(() => Math.random() - 0.5);
    pathList.sort(() => Math.random() - 0.5);

    return { settlements: settlementList, paths: pathList };
  }, []);

  const visibleSettlementsCount = Math.floor(progress * settlements.length);
  const pathProgress = Math.max(0, (progress - 0.1) / 0.9); // Paths start after 10% of settlements appear
  const visiblePathsCount = Math.floor(pathProgress * paths.length);

  return (
    <div className="absolute inset-0 overflow-hidden" data-testid="agriculture-cities-container">
      <svg viewBox="0 0 100 100" className="w-full h-full" preserveAspectRatio="none">
        <defs>
          <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="0.5" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>

        {/* Paths (Roads/Rivers) - Thicker and more organic */}
        {paths.slice(0, visiblePathsCount).map(({ id, d }, index) => (
          <motion.path
            key={id}
            d={d}
            stroke={pathColor}
            strokeWidth={0.35} // Increased thickness
            fill="none"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 1 }}
            transition={{ duration: 4, delay: index * 0.05, ease: 'easeOut' }}
          />
        ))}

        {/* Settlements - Organic Shapes with Outlines and Glow */}
        {settlements.slice(0, visibleSettlementsCount).map(({ id, d, color }, index) => (
          <motion.path
            key={id}
            d={d}
            fill={`${color}99`} // Semi-transparent fill (~60%)
            stroke={color}
            strokeWidth={0.15}
            filter="url(#glow)"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{
              opacity: 1,
              scale: 1,
            }}
            transition={{
              duration: 2.5,
              delay: index * 0.1,
              ease: 'easeOut',
            }}
          />
        ))}
      </svg>
    </div>
  );
};

export default AgricultureAndCitiesShapes;
