'use client';

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

interface AgricultureAndCitiesShapesProps {
  progress: number;
}

import { Delaunay } from 'd3-delaunay';

type Point = [number, number];

const AgricultureAndCitiesShapes: React.FC<AgricultureAndCitiesShapesProps> = ({ progress }) => {
  const voronoiData = useMemo(() => {
    const points: Point[] = Array.from({ length: 150 }, () => [Math.random() * 100, Math.random() * 100]);
    const delaunay = Delaunay.from(points);
    const voronoi = delaunay.voronoi([0, 0, 100, 100]);

    const cells = Array.from(voronoi.cellPolygons());
    // Shuffle for a more organic appearance
    cells.sort(() => Math.random() - 0.5);

    return { cells, points };
  }, []);

  const visibleCellCount = Math.floor(progress * voronoiData.cells.length);
  const visibleCells = voronoiData.cells.slice(0, visibleCellCount);

  // Nodes (settlements) appear after 30% of the land plots are drawn
  const settlementProgress = Math.max(0, (progress - 0.3) / 0.7);
  const visibleNodeCount = Math.floor(settlementProgress * voronoiData.points.length);
  const visiblePoints = voronoiData.points.slice(0, visibleNodeCount);

  return (
    <div className="absolute inset-0 overflow-hidden" data-testid="agriculture-cities-container">
      <svg viewBox="0 0 100 100" className="w-full h-full" preserveAspectRatio="none">
        <defs>
          <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="0.5" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>
        <g>
          {visibleCells.map((cell, index) => (
            <motion.path
              key={`cell-${index}`}
              d={`M${cell.join("L")}Z`}
              fill="rgba(255, 255, 255, 0.03)"
              stroke="rgba(255, 255, 255, 0.2)"
              strokeWidth={0.1}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1.5, delay: index * 0.02 }}
            />
          ))}
        </g>
        <g filter="url(#glow)">
          {visiblePoints.map(([px, py], index) => (
            <motion.circle
              key={`point-${index}`}
              cx={px}
              cy={py}
              r={0.3}
              fill="rgba(255, 215, 0, 0.8)"
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 2, delay: index * 0.03 }}
            />
          ))}
        </g>
      </svg>
    </div>
  );
};

export default AgricultureAndCitiesShapes;
