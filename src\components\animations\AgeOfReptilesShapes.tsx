'use client';

import React, { useEffect } from 'react';
import { motion, useAnimation } from 'framer-motion';
import DistantBirds from './DistantBirds';





// Renders a single, extra-wide, seamlessly scrolling SVG layer.
const HillLayer: React.FC<{
  d: string;
  fill: string;
  duration: number;
  zIndex: number;
}> = ({ d, fill, duration, zIndex }) => (
  <motion.div
    className="absolute inset-0 w-[300%] h-full"
    style={{ zIndex }}
    animate={{ x: ['0%', '-66.6667%'] }} // Animate 2/3 of the width for a seamless loop
    transition={{
      duration,
      repeat: Infinity,
      ease: 'linear',
    }}
  >
    <svg
      viewBox="0 0 4500 800" // Extra-wide viewBox for a single SVG
      className="w-full h-full"
      preserveAspectRatio="none"
      aria-hidden="true"
    >
      <path d={d} fill={fill} />
    </svg>
  </motion.div>
);

interface AgeOfReptilesShapesProps {
  currentTime: number;
}

const AgeOfReptilesShapes: React.FC<AgeOfReptilesShapesProps> = ({ currentTime }) => {
  const showAsteroid = currentTime >= 476;
  const sunControls = useAnimation();
  const contentControls = useAnimation();
  const containerControls = useAnimation();

  const shakeVariants = {
    shaking: {
      x: [0, -2, 2, -2, 2, 0],
      y: [0, 1, -1, 1, -1, 0],
      transition: { duration: 0.5, repeat: Infinity },
    },
    stop: { x: 0, y: 0 },
  };

  useEffect(() => {
    const sequence = async () => {
      sunControls.start({
        opacity: 1,
        transition: { duration: 2, ease: 'easeInOut' },
      });

      await new Promise(resolve => setTimeout(resolve, 1000));
      contentControls.start({
        opacity: 1,
        transition: { duration: 10, ease: 'easeInOut' },
      });

      if (showAsteroid) {
        containerControls.start('shaking');

        setTimeout(() => {
          containerControls.start({
            x: [0, -10, 10, -10, 10, 0],
            y: [0, 5, -5, 5, -5, 0],
            transition: { duration: 0.2, repeat: Infinity },
          });
        }, 8500);
      }
    };

    sequence();
  }, [sunControls, contentControls, containerControls, showAsteroid]);
  const hills = [
    // Each path is 300% wide (4500 units) and designed to be perfectly tileable with double the detail.
    // The animation moves the shape by 200% of the viewport width (3000 units), creating a seamless loop.
    
    // Farthest, most rocky and mountainous (most transparent)
    { d: 'M0,620 L125,590 L188,580 L250,600 L375,560 L450,580 L563,550 L650,590 L750,630 L850,600 L938,580 L1050,610 L1125,550 L1250,590 L1313,600 L1425,580 L1500,640 L1600,610 L1688,600 L1750,620 L1875,570 L2000,610 L2063,600 L2150,615 L2250,620 L2375,580 L2438,590 L2550,570 L2625,580 L2750,610 L2813,620 L2900,630 L3000,650 L3100,620 L3188,610 L3300,640 L3375,590 L3450,610 L3563,630 L3650,640 L3750,660 L3850,630 L3938,620 L4050,640 L4125,600 L4250,630 L4313,610 L4400,630 L4500,620 V800 H0 Z', fill: 'rgba(33, 31, 29, 0.2)', duration: 360, zIndex: 1 },
    // A bit closer, still rocky
    { d: 'M0,670 L150,640 L225,630 L350,650 Q450,610 600,630 T750,650 L900,680 L1000,660 L1125,650 T1350,640 L1500,670 T1800,660 L2000,680 T2250,650 L2400,640 L2475,630 Q2700,620 2850,640 T3150,680 L3300,670 Q3600,700 3800,680 T4200,690 L4500,670 V800 H0 Z', fill: 'rgba(33, 31, 29, 0.4)', duration: 270, zIndex: 2 },
    // Mid-ground, more curves
    { d: 'M0,730 C300,680,500,760,900,720 C1300,680,1500,770,1800,740 C2100,710,2500,780,2700,750 C3000,720,3300,790,3600,760 C3900,730,4200,800,4500,730 V800 H0 Z', fill: 'rgba(33, 31, 29, 0.6)', duration: 180, zIndex: 3 },
    // Closer, smoother
    { d: 'M0,780 C450,740,750,800,1125,770 C1500,740,1800,810,2250,780 C2700,750,3150,820,3600,790 C3900,760,4200,830,4500,780 V800 H0 Z', fill: 'rgba(33, 31, 29, 0.8)', duration: 135, zIndex: 4 },
    // Nearest, very smooth (almost black)
    { d: 'M0,800 C800,750,1600,830,2250,800 C2900,770,3700,850,4500,800 V800 H0 Z', fill: '#211F1D', duration: 100, zIndex: 5 },
  ];

  return (
    <div className="absolute inset-0 w-full h-full overflow-hidden">
      <motion.div
        className="absolute inset-0 w-full h-full"
        animate={containerControls}
        variants={shakeVariants}
      >
        {/* Sun - fades in immediately */}
        <motion.div
          className="absolute top-[10%] left-[15%] w-12 h-12"
          style={{ zIndex: 0 }}
          initial={{ opacity: 0 }}
          animate={sunControls}
        >
          <svg viewBox="0 0 100 100" className="w-full h-full" aria-hidden="true">
            <circle cx="50" cy="50" r="50" fill="#FFFDE7" />
          </svg>
        </motion.div>

        {/* Container for elements that fade in slowly */}
        <motion.div className="absolute inset-0 w-full h-full" initial={{ opacity: 0 }} animate={contentControls}>
          {/* The container for the asteroid needs to be outside the clipped area of birds */}
          <DistantBirds />


          <div className="relative w-full h-full">
            {hills.map((hill, i) => (
              <HillLayer key={i} {...hill} />
            ))}
          </div>
        </motion.div>
      </motion.div>

      {/* Impact effects container - SEPARATE from the animated container */}
      <motion.div
        className="absolute inset-0 w-full h-full bg-white"
        initial={{ opacity: 0 }}
       // animate={flashControls}
        style={{ zIndex: 10 }}
      />
      <motion.div
        className="absolute inset-0 w-full h-full bg-black"
        initial={{ opacity: 0 }}
       // animate={blackoutControls}
        style={{ zIndex: 9 }}
      />
    </div>
  );
};

export default AgeOfReptilesShapes;
