import { NextResponse } from 'next/server';
import { kv } from '@vercel/kv';

export async function GET() {
  try {
    // Atomically fetch both vote counts
    const [utopiaVotes, dystopiaVotes] = await Promise.all([
      kv.get('soundscape:votes:utopia'),
      kv.get('soundscape:votes:dystopia'),
    ]);

    return NextResponse.json({
      utopia: utopiaVotes || 0,
      dystopia: dystopiaVotes || 0,
    }, { status: 200 });

  } catch (error) {
    console.error('Error fetching vote counts:', error);
    return NextResponse.json({ message: 'An error occurred while fetching vote counts.' }, { status: 500 });
  }
}
